import os
import sys
import yaml
from dotenv import load_dotenv
import argparse
import logging

def parse_arguments():
    """解析命令行参数"""
    parser = argparse.ArgumentParser(description='AI Camera Service')
    
    # 服务器配置
    parser.add_argument('--host', type=str, default='0.0.0.0',
                      help='服务器主机地址 (默认: 0.0.0.0)')
    parser.add_argument('--port', type=int, default=8102,
                      help='服务器端口号 (默认: 8101)')
    
    # 配置文件路径
    parser.add_argument('--env-config', type=str, default='configs/env.yaml',
                      help='环境配置文件路径 (默认: configs/env.yaml)')
    parser.add_argument('--env-file', type=str, default='configs/.env',
                      help='环境变量文件路径 (默认: configs/.env)')
    
    return parser.parse_args()

class Config:
    """配置类，用于统一管理所有配置"""
    _instance = None  # 单例模式的实例存储

    def __new__(cls, args=None):
        if cls._instance is None:
            cls._instance = super().__new__(cls)
            cls._instance._initialized = False
        return cls._instance

    def __init__(self, args=None):
        # 确保初始化只执行一次
        if self._initialized:
            return

        # 如果没有传入参数，则解析命令行参数
        self.args = args if args is not None else parse_arguments()

        # 统一加载环境变量
        self.env_path = self.args.env_file
        load_dotenv(dotenv_path=self.env_path)

        # 加载 YAML 配置
        self.env = self.load_yaml_config(self.args.env_config)

        # 环境变量
        self.env_vars = dict(os.environ)

        # 服务器配置
        self.host = self.args.host
        self.port = self.args.port

        # YOLO服务配置
        self._init_yolo_service_config()

        self._initialized = True

    def _init_yolo_service_config(self):
        """初始化YOLO服务配置"""
        # YOLO服务基础配置
        self.yolo_service = {
            # 服务端口配置
            'host': '0.0.0.0',
            'port': 8001,

            # 模型路径配置（复用现有模型）
            'models': {
                'dipper': self.env.get('yolo_model', 'llms/models/yolo/best-dipper.pt'),
                'filter': self.env.get('yolo_model_filter', 'llms/models/yolo/best-filter.pt'),
                'shaft': self.env.get('yolo_model_bucket_shaft', 'llms/models/yolo/best-dipper-shaft.pt'),
                'aerobic': 'llms/models/yolo/best-aerobic.pt',
                'piezha': self.env.get('yolo_model_piezha', 'llms/models/yolo/best-piezha.pt'),
                'segmentation': 'llms/models/yolo/best.pt'
            },

            # 服务启用配置
            'enabled_services': {
                'dipper_detection': True,
                'filter_detection': True,
                'shaft_detection': True,
                'aerobic_detection': False,  # 默认关闭，按需启用
                'piezha_detection': False,   # 默认关闭，按需启用
                'segmentation': False        # 默认关闭，按需启用
            },

            # 模型预加载配置
            'preload_models': True,  # 是否在服务启动时预加载模型

            # 并发配置
            'max_workers': 4,  # 最大工作线程数
            'max_concurrent_requests': 10,  # 最大并发请求数

            # 推理参数配置
            'inference_params': {
                'dipper': {
                    'conf': 0.5,
                    'iou': 0.4,
                    'max_det': 3,
                    'imgsz': 640,
                    'device': 'cpu'
                },
                'filter': {
                    'conf': 0.6,
                    'iou': 0.6,
                    'max_det': 10,
                    'imgsz': 640,
                    'device': 'cpu'
                },
                'shaft': {
                    'conf': 0.25,
                    'iou': 0.2,
                    'max_det': 5,
                    'imgsz': 640,
                    'device': 'cpu'
                },
                'aerobic': {
                    'conf': 0.5,
                    'iou': 0.4,
                    'max_det': 10,
                    'imgsz': 640,
                    'device': 'cpu'
                },
                'segmentation': {
                    'conf': 0.25,
                    'iou': 0.7,
                    'imgsz': 640,
                    'device': 'cpu'
                }
            },

            # 结果保存配置
            'save_results': {
                'enabled': True,
                'output_dir': 'yolo-service/outputs',
                'upload_dir': 'yolo-service/uploads',
                'max_file_size': 10 * 1024 * 1024,  # 10MB
                'allowed_extensions': ['.jpg', '.jpeg', '.png', '.bmp', '.tiff', '.webp'],
                'cleanup_days': 7  # 清理7天前的文件
            },

            # API配置
            'api': {
                'title': 'YOLO推理服务API',
                'description': '基于YOLO的多模型推理Web API服务',
                'version': '1.0.0',
                'docs_url': '/docs',
                'redoc_url': '/redoc',
                'cors_enabled': True,
                'cors_origins': ['*']  # 生产环境中应该限制具体域名
            }
        }
    
    @staticmethod
    def load_yaml_config(yaml_file):
        """加载 YAML 配置文件"""
        try:
            with open(yaml_file, 'r', encoding='utf-8') as f:
                return yaml.safe_load(f)
        except Exception as e:
            # print(f"加载配置文件 {yaml_file} 失败: {str(e)}")
            logging.error(f"加载配置文件 {yaml_file} 失败: {str(e)}")
            return None
            
    @staticmethod
    def load_custom_yaml(yaml_path):
        """加载自定义的 YAML 配置文件"""
        return Config.load_yaml_config(yaml_path)

# 创建全局配置实例
config = Config()

if __name__ == '__main__':
    print(config.host)
    print(config.port)
    print(config.env)
    print(config.env_vars)