"""
并发请求限制器

使用asyncio.Semaphore实现max_concurrent_requests限制
"""

import asyncio
import time
import logging
from typing import Dict, Optional
from contextlib import asynccontextmanager

# 初始化日志
logger = logging.getLogger(__name__)


class ConcurrencyLimiter:
    """并发限制器"""
    
    def __init__(self, max_concurrent: int = 10):
        """
        初始化并发限制器
        
        Args:
            max_concurrent: 最大并发请求数
        """
        self.max_concurrent = max_concurrent
        self.semaphore = asyncio.Semaphore(max_concurrent)
        self.active_requests = 0
        self.total_requests = 0
        self.rejected_requests = 0
        self.start_time = time.time()
        self._lock = asyncio.Lock()
        
        logger.info(f"并发限制器初始化完成，最大并发数: {max_concurrent}")
    
    @asynccontextmanager
    async def acquire(self, timeout: Optional[float] = None):
        """
        获取并发许可
        
        Args:
            timeout: 超时时间（秒）
            
        Yields:
            bool: 是否成功获取许可
            
        Raises:
            asyncio.TimeoutError: 超时
        """
        acquired = False
        
        try:
            # 尝试获取信号量
            if timeout:
                await asyncio.wait_for(self.semaphore.acquire(), timeout=timeout)
            else:
                await self.semaphore.acquire()
            
            acquired = True
            
            # 更新统计信息
            async with self._lock:
                self.active_requests += 1
                self.total_requests += 1
            
            logger.debug(f"获取并发许可成功，当前活跃请求: {self.active_requests}")
            
            yield True
            
        except asyncio.TimeoutError:
            async with self._lock:
                self.rejected_requests += 1
            
            logger.warning(f"并发请求超时，当前活跃请求: {self.active_requests}")
            raise
            
        except Exception as e:
            async with self._lock:
                self.rejected_requests += 1
            
            logger.error(f"获取并发许可失败: {e}")
            raise
            
        finally:
            if acquired:
                # 释放信号量
                self.semaphore.release()
                
                # 更新统计信息
                async with self._lock:
                    self.active_requests -= 1
                
                logger.debug(f"释放并发许可，当前活跃请求: {self.active_requests}")
    
    async def get_stats(self) -> Dict[str, any]:
        """
        获取统计信息
        
        Returns:
            Dict: 统计信息
        """
        async with self._lock:
            uptime = time.time() - self.start_time
            
            return {
                "max_concurrent": self.max_concurrent,
                "active_requests": self.active_requests,
                "total_requests": self.total_requests,
                "rejected_requests": self.rejected_requests,
                "success_rate": (self.total_requests - self.rejected_requests) / max(self.total_requests, 1) * 100,
                "uptime_seconds": uptime,
                "requests_per_second": self.total_requests / max(uptime, 1)
            }
    
    def update_limit(self, new_limit: int):
        """
        更新并发限制
        
        Args:
            new_limit: 新的并发限制
        """
        if new_limit <= 0:
            raise ValueError("并发限制必须大于0")
        
        old_limit = self.max_concurrent
        self.max_concurrent = new_limit
        
        # 创建新的信号量
        self.semaphore = asyncio.Semaphore(new_limit)
        
        logger.info(f"并发限制已更新: {old_limit} -> {new_limit}")
    
    async def reset_stats(self):
        """重置统计信息"""
        async with self._lock:
            self.total_requests = 0
            self.rejected_requests = 0
            self.start_time = time.time()
        
        logger.info("并发限制器统计信息已重置")


class ModelConcurrencyManager:
    """模型并发管理器"""
    
    def __init__(self):
        """初始化模型并发管理器"""
        self.limiters: Dict[str, ConcurrencyLimiter] = {}
        self.global_limiter: Optional[ConcurrencyLimiter] = None
        self._lock = asyncio.Lock()
    
    def set_global_limit(self, max_concurrent: int):
        """
        设置全局并发限制
        
        Args:
            max_concurrent: 最大并发请求数
        """
        self.global_limiter = ConcurrencyLimiter(max_concurrent)
        logger.info(f"全局并发限制已设置: {max_concurrent}")
    
    def set_model_limit(self, model_name: str, max_concurrent: int):
        """
        设置特定模型的并发限制
        
        Args:
            model_name: 模型名称
            max_concurrent: 最大并发请求数
        """
        self.limiters[model_name] = ConcurrencyLimiter(max_concurrent)
        logger.info(f"模型 {model_name} 并发限制已设置: {max_concurrent}")
    
    @asynccontextmanager
    async def acquire_for_model(self, model_name: str, timeout: Optional[float] = None):
        """
        为特定模型获取并发许可
        
        Args:
            model_name: 模型名称
            timeout: 超时时间
            
        Yields:
            bool: 是否成功获取许可
        """
        # 首先尝试获取全局许可
        if self.global_limiter:
            async with self.global_limiter.acquire(timeout=timeout):
                # 然后尝试获取模型特定许可
                if model_name in self.limiters:
                    async with self.limiters[model_name].acquire(timeout=timeout):
                        yield True
                else:
                    yield True
        else:
            # 只获取模型特定许可
            if model_name in self.limiters:
                async with self.limiters[model_name].acquire(timeout=timeout):
                    yield True
            else:
                # 没有任何限制
                yield True
    
    async def get_all_stats(self) -> Dict[str, any]:
        """
        获取所有限制器的统计信息
        
        Returns:
            Dict: 统计信息
        """
        stats = {
            "global": None,
            "models": {}
        }
        
        # 全局统计
        if self.global_limiter:
            stats["global"] = await self.global_limiter.get_stats()
        
        # 模型统计
        for model_name, limiter in self.limiters.items():
            stats["models"][model_name] = await limiter.get_stats()
        
        return stats
    
    def remove_model_limit(self, model_name: str):
        """
        移除特定模型的并发限制
        
        Args:
            model_name: 模型名称
        """
        if model_name in self.limiters:
            del self.limiters[model_name]
            logger.info(f"模型 {model_name} 的并发限制已移除")


# 全局并发管理器实例
concurrency_manager = ModelConcurrencyManager()
