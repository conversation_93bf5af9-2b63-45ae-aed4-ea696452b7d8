"""
API中间件

提供请求处理、日志记录、错误处理等中间件功能
"""

import time
import logging
import uuid
from typing import Callable
from fastapi import Request, Response, HTTPException
from fastapi.middleware.base import BaseHTTPMiddleware
from fastapi.middleware.cors import CORSMiddleware
from starlette.responses import JSONResponse

# 初始化日志
logger = logging.getLogger(__name__)


class RequestLoggingMiddleware(BaseHTTPMiddleware):
    """请求日志中间件"""
    
    async def dispatch(self, request: Request, call_next: Callable) -> Response:
        """处理请求并记录日志"""
        # 生成请求ID
        request_id = str(uuid.uuid4())[:8]
        
        # 记录请求开始
        start_time = time.time()
        logger.info(f"[{request_id}] {request.method} {request.url} - 开始处理")
        
        # 添加请求ID到请求状态
        request.state.request_id = request_id
        
        try:
            # 处理请求
            response = await call_next(request)
            
            # 计算处理时间
            process_time = time.time() - start_time
            
            # 记录请求完成
            logger.info(
                f"[{request_id}] {request.method} {request.url} - "
                f"状态码: {response.status_code}, 耗时: {process_time:.3f}s"
            )
            
            # 添加响应头
            response.headers["X-Request-ID"] = request_id
            response.headers["X-Process-Time"] = f"{process_time:.3f}"
            
            return response
            
        except Exception as e:
            # 计算处理时间
            process_time = time.time() - start_time
            
            # 记录错误
            logger.error(
                f"[{request_id}] {request.method} {request.url} - "
                f"错误: {str(e)}, 耗时: {process_time:.3f}s"
            )
            
            # 返回错误响应
            return JSONResponse(
                status_code=500,
                content={
                    "detail": "内部服务器错误",
                    "request_id": request_id,
                    "error": str(e)
                },
                headers={
                    "X-Request-ID": request_id,
                    "X-Process-Time": f"{process_time:.3f}"
                }
            )


class RateLimitMiddleware(BaseHTTPMiddleware):
    """简单的速率限制中间件"""
    
    def __init__(self, app, max_requests: int = 100, window_seconds: int = 60):
        super().__init__(app)
        self.max_requests = max_requests
        self.window_seconds = window_seconds
        self.requests = {}  # {client_ip: [(timestamp, count), ...]}
    
    async def dispatch(self, request: Request, call_next: Callable) -> Response:
        """检查速率限制"""
        client_ip = request.client.host if request.client else "unknown"
        current_time = time.time()
        
        # 清理过期记录
        if client_ip in self.requests:
            self.requests[client_ip] = [
                (timestamp, count) for timestamp, count in self.requests[client_ip]
                if current_time - timestamp < self.window_seconds
            ]
        
        # 检查当前请求数
        if client_ip in self.requests:
            total_requests = sum(count for _, count in self.requests[client_ip])
            if total_requests >= self.max_requests:
                logger.warning(f"速率限制触发: {client_ip} - {total_requests} 请求")
                return JSONResponse(
                    status_code=429,
                    content={
                        "detail": "请求过于频繁，请稍后重试",
                        "max_requests": self.max_requests,
                        "window_seconds": self.window_seconds
                    }
                )
        
        # 记录当前请求
        if client_ip not in self.requests:
            self.requests[client_ip] = []
        self.requests[client_ip].append((current_time, 1))
        
        # 继续处理请求
        return await call_next(request)


class SecurityHeadersMiddleware(BaseHTTPMiddleware):
    """安全头中间件"""
    
    async def dispatch(self, request: Request, call_next: Callable) -> Response:
        """添加安全响应头"""
        response = await call_next(request)
        
        # 添加安全头
        response.headers["X-Content-Type-Options"] = "nosniff"
        response.headers["X-Frame-Options"] = "DENY"
        response.headers["X-XSS-Protection"] = "1; mode=block"
        response.headers["Referrer-Policy"] = "strict-origin-when-cross-origin"
        
        return response


class ErrorHandlingMiddleware(BaseHTTPMiddleware):
    """错误处理中间件"""
    
    async def dispatch(self, request: Request, call_next: Callable) -> Response:
        """统一错误处理"""
        try:
            return await call_next(request)
        
        except HTTPException as e:
            # FastAPI HTTP异常，直接传递
            raise e
        
        except ValueError as e:
            # 参数错误
            logger.warning(f"参数错误: {str(e)}")
            return JSONResponse(
                status_code=400,
                content={
                    "detail": "请求参数错误",
                    "error": str(e)
                }
            )
        
        except FileNotFoundError as e:
            # 文件不存在
            logger.warning(f"文件不存在: {str(e)}")
            return JSONResponse(
                status_code=404,
                content={
                    "detail": "请求的文件不存在",
                    "error": str(e)
                }
            )
        
        except PermissionError as e:
            # 权限错误
            logger.error(f"权限错误: {str(e)}")
            return JSONResponse(
                status_code=403,
                content={
                    "detail": "权限不足",
                    "error": str(e)
                }
            )
        
        except Exception as e:
            # 其他未处理的异常
            logger.error(f"未处理的异常: {str(e)}", exc_info=True)
            return JSONResponse(
                status_code=500,
                content={
                    "detail": "内部服务器错误",
                    "error": "服务器内部错误，请联系管理员"
                }
            )


def setup_cors_middleware(app, config: dict):
    """设置CORS中间件"""
    cors_config = config.get('cors', {})
    
    if cors_config.get('enabled', True):
        app.add_middleware(
            CORSMiddleware,
            allow_origins=cors_config.get('origins', ["*"]),
            allow_credentials=cors_config.get('credentials', True),
            allow_methods=cors_config.get('methods', ["*"]),
            allow_headers=cors_config.get('headers', ["*"])
        )
        logger.info("CORS中间件已启用")


def setup_middleware(app, config: dict):
    """设置所有中间件"""
    # 错误处理中间件（最外层）
    app.add_middleware(ErrorHandlingMiddleware)
    
    # 安全头中间件
    app.add_middleware(SecurityHeadersMiddleware)
    
    # 请求日志中间件
    app.add_middleware(RequestLoggingMiddleware)
    
    # 速率限制中间件（可选）
    rate_limit_config = config.get('rate_limit', {})
    if rate_limit_config.get('enabled', False):
        app.add_middleware(
            RateLimitMiddleware,
            max_requests=rate_limit_config.get('max_requests', 100),
            window_seconds=rate_limit_config.get('window_seconds', 60)
        )
        logger.info("速率限制中间件已启用")
    
    # CORS中间件（最内层）
    setup_cors_middleware(app, config)
    
    logger.info("所有中间件已设置完成")
