#!/usr/bin/env python3
"""
YOLO服务启动脚本

用于快速启动和测试YOLO推理服务
"""

import os
import sys
import time
import argparse
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent.parent
sys.path.append(str(project_root))

def check_dependencies():
    """检查依赖项"""
    print("🔍 检查依赖项...")
    
    required_packages = [
        'fastapi',
        'uvicorn',
        'ultralytics',
        'opencv-python',
        'numpy',
        'pydantic'
    ]
    
    missing_packages = []
    
    for package in required_packages:
        try:
            __import__(package.replace('-', '_'))
            print(f"✅ {package}")
        except ImportError:
            print(f"❌ {package} - 未安装")
            missing_packages.append(package)
    
    if missing_packages:
        print(f"\n⚠️  缺少依赖包: {', '.join(missing_packages)}")
        print("请运行以下命令安装:")
        print(f"pip install {' '.join(missing_packages)}")
        return False
    
    print("✅ 所有依赖项检查通过")
    return True


def check_models():
    """检查模型文件"""
    print("\n🔍 检查模型文件...")
    
    model_paths = [
        "llms/models/yolo/best-dipper.pt",
        "llms/models/yolo/best-filter.pt", 
        "llms/models/yolo/best-dipper-shaft.pt",
        "llms/models/yolo/best.pt"
    ]
    
    missing_models = []
    
    for model_path in model_paths:
        full_path = project_root / model_path
        if full_path.exists():
            print(f"✅ {model_path}")
        else:
            print(f"❌ {model_path} - 文件不存在")
            missing_models.append(model_path)
    
    if missing_models:
        print(f"\n⚠️  缺少模型文件: {len(missing_models)} 个")
        print("注意: 某些功能可能无法正常工作")
        return False
    
    print("✅ 所有模型文件检查通过")
    return True


def check_directories():
    """检查必要的目录"""
    print("\n🔍 检查目录结构...")
    
    required_dirs = [
        "yolo-service/uploads",
        "yolo-service/outputs",
        "logs"
    ]
    
    for dir_path in required_dirs:
        full_path = project_root / dir_path
        if not full_path.exists():
            print(f"📁 创建目录: {dir_path}")
            full_path.mkdir(parents=True, exist_ok=True)
        else:
            print(f"✅ {dir_path}")
    
    print("✅ 目录结构检查通过")
    return True


def run_basic_tests():
    """运行基本测试"""
    print("\n🧪 运行基本测试...")
    
    try:
        # 导入测试模块
        from tests.test_basic_functionality import run_basic_tests
        
        # 运行测试
        success = run_basic_tests()
        
        if success:
            print("✅ 基本测试通过")
            return True
        else:
            print("❌ 基本测试失败")
            return False
            
    except Exception as e:
        print(f"❌ 测试运行失败: {e}")
        return False


def start_service(host="0.0.0.0", port=8001, reload=False):
    """启动服务"""
    print(f"\n🚀 启动YOLO推理服务...")
    print(f"📍 地址: http://{host}:{port}")
    print(f"📚 文档: http://{host}:{port}/docs")
    
    try:
        # 导入应用
        from app import create_app
        import uvicorn
        
        # 创建应用
        app = create_app()
        
        # 启动服务
        uvicorn.run(
            app,
            host=host,
            port=port,
            reload=reload,
            log_level="info"
        )
        
    except KeyboardInterrupt:
        print("\n👋 服务已停止")
    except Exception as e:
        print(f"❌ 服务启动失败: {e}")
        return False
    
    return True


def main():
    """主函数"""
    parser = argparse.ArgumentParser(description="YOLO服务启动脚本")
    
    parser.add_argument("--host", default="0.0.0.0", help="服务器主机地址")
    parser.add_argument("--port", type=int, default=8001, help="服务器端口")
    parser.add_argument("--reload", action="store_true", help="开发模式，自动重载")
    parser.add_argument("--skip-checks", action="store_true", help="跳过检查步骤")
    parser.add_argument("--skip-tests", action="store_true", help="跳过测试步骤")
    parser.add_argument("--check-only", action="store_true", help="只进行检查，不启动服务")
    
    args = parser.parse_args()
    
    print("=" * 60)
    print("🎯 YOLO推理服务启动脚本")
    print("=" * 60)
    
    # 检查步骤
    if not args.skip_checks:
        checks_passed = True
        
        # 检查依赖项
        if not check_dependencies():
            checks_passed = False
        
        # 检查目录
        if not check_directories():
            checks_passed = False
        
        # 检查模型文件
        if not check_models():
            print("⚠️  模型文件检查未完全通过，但服务仍可启动")
        
        if not checks_passed:
            print("\n❌ 检查未通过，请解决上述问题后重试")
            return 1
    
    # 测试步骤
    if not args.skip_tests and not args.check_only:
        if not run_basic_tests():
            print("\n⚠️  基本测试未通过，但服务仍可启动")
            response = input("是否继续启动服务? (y/N): ")
            if response.lower() != 'y':
                return 1
    
    # 如果只是检查，到此结束
    if args.check_only:
        print("\n✅ 检查完成")
        return 0
    
    # 启动服务
    try:
        start_service(args.host, args.port, args.reload)
        return 0
    except Exception as e:
        print(f"\n❌ 启动失败: {e}")
        return 1


if __name__ == "__main__":
    sys.exit(main())
