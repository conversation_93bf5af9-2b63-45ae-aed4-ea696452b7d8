"""
耙斗检测器

基于现有yolo_api_server.py的优秀实现，提供耙斗倾斜检测功能
"""

import os
import sys
import logging
from typing import Dict, List, Any, Union
from pathlib import Path
import numpy as np
import cv2

# 添加项目根目录到Python路径
project_root = Path(__file__).parent.parent.parent
sys.path.append(str(project_root))

from server.utils.logger import setup_logging
from ..core.base_detector import BaseDetector

# 尝试导入图像处理工具
try:
    from llms.utils.transform_processor import process_image
    HAS_TRANSFORM_PROCESSOR = True
except ImportError:
    HAS_TRANSFORM_PROCESSOR = False
    logging.warning("transform_processor不可用，将跳过图像预处理")

# 初始化日志
setup_logging()
logger = logging.getLogger(__name__)


class DipperDetector(BaseDetector):
    """
    耙斗检测器
    
    功能：
    - 检测耙斗设备的倾斜状态
    - 根据位置自动分配设备ID
    - 支持图像预处理和结果标注
    """
    
    def __init__(self, model_info):
        """
        初始化耙斗检测器
        
        Args:
            model_info: 模型配置信息
        """
        super().__init__(model_info)
        self.transform_params_file = 'llms/utils/split_coords/pre_line.txt'
        logger.info("耙斗检测器初始化完成")
    
    def _preprocess_image(self, image: np.ndarray) -> np.ndarray:
        """
        图像预处理
        
        Args:
            image: 输入图像
            
        Returns:
            np.ndarray: 处理后的图像
        """
        if not HAS_TRANSFORM_PROCESSOR:
            logger.debug("跳过图像预处理")
            return image
        
        try:
            # 使用现有的图像处理逻辑
            processed_image = process_image(image, params_file=self.transform_params_file)
            if processed_image is None:
                logger.warning("图像处理失败，使用原图")
                return image
            
            logger.debug("图像预处理完成")
            return processed_image
            
        except Exception as e:
            logger.warning(f"图像预处理失败: {e}，使用原图")
            return image
    
    def _parse_results(self, results: Any, image_shape: tuple) -> List[Dict[str, Any]]:
        """
        解析推理结果
        
        Args:
            results: YOLO推理结果
            image_shape: 图像尺寸 (height, width, channels)
            
        Returns:
            List[Dict]: 解析后的检测结果
        """
        devices = []
        
        if len(results) == 0:
            return devices
        
        result = results[0]  # 获取第一张图片的结果
        boxes = result.boxes
        
        if boxes is None or len(boxes) == 0:
            return devices
        
        # 获取图像宽度用于位置划分
        img_width = image_shape[1]
        
        # 定义三个设备位置的区域范围(按图像宽度的比例划分)
        position_ranges = [
            (0, img_width / 3),                    # 位置1 (左)
            (img_width / 3, 2 * img_width / 3),    # 位置2 (中)
            (2 * img_width / 3, img_width)         # 位置3 (右)
        ]
        
        logger.debug(f'位置范围: {position_ranges}')
        
        # 处理检测框
        for i in range(len(boxes)):
            box = boxes[i]
            bbox = box.xyxy[0].cpu().numpy().tolist()  # 边界框坐标
            
            # 计算边界框中心点x坐标
            box_center_x = (bbox[0] + bbox[2]) / 2
            
            # 根据中心点确定设备位置(1,2,3)
            device_id = None
            for pos_id, (start, end) in enumerate(position_ranges):
                if start <= box_center_x < end:
                    device_id = pos_id + 1  # 设备ID为位置索引+1
                    break
            
            # 如果无法确定位置(极少情况)，则使用索引+1作为设备ID
            if device_id is None:
                device_id = i + 1
            
            cls_name = result.names[int(box.cls[0])]  # 获取类别名称
            conf = float(box.conf[0])  # 置信度
            
            devices.append({
                "device_id": device_id,
                "status": cls_name,
                "confidence": conf,
                "bbox": bbox,
                "position": f"位置{device_id}",  # 添加位置描述便于理解
                "center_x": box_center_x
            })
        
        # 按device_id排序
        devices.sort(key=lambda x: x["device_id"])
        
        return devices
    
    def _add_custom_annotations(self, image: np.ndarray, devices: List[Dict]) -> np.ndarray:
        """
        添加自定义标注
        
        Args:
            image: 标注图像
            devices: 检测结果
            
        Returns:
            np.ndarray: 添加标注后的图像
        """
        annotated_img = image.copy()
        
        # 在图像上标注设备ID
        for device in devices:
            bbox = device["bbox"]
            device_id = device["device_id"]
            x, y = int(bbox[0]), int(bbox[1]) - 10  # 在边界框左上角上方显示设备ID
            
            # 添加设备ID标注
            cv2.putText(annotated_img, f"ID: {device_id}", (x, y), 
                       cv2.FONT_HERSHEY_SIMPLEX, 0.9, (0, 255, 0), 2)
        
        return annotated_img
    
    def detect_devices(self, image: Union[np.ndarray, str], 
                      save_result: bool = False) -> Dict[str, Any]:
        """
        检测耙斗设备状态（兼容原有接口）
        
        Args:
            image: 输入图像数组或图片路径
            save_result: 是否保存识别结果图像
            
        Returns:
            Dict: 检测结果，包含devices列表和可选的image_path
        """
        result = self.detect(image, save_result=save_result)
        
        # 转换为原有接口格式
        response = {
            "devices": result.get("devices", [])
        }
        
        if "image_path" in result:
            response["image_path"] = result["image_path"]
        
        return response


def get_detector(model_path: str = "llms/models/yolo/best-dipper.pt"):
    """
    线程安全的检测器获取函数（兼容原有接口）
    
    Args:
        model_path: YOLO模型路径
        
    Returns:
        DipperDetector: 新的检测器实例
    """
    # 创建简单的模型信息对象
    class SimpleModelInfo:
        def __init__(self, path):
            self.name = "dipper"
            self.path = path
            self.model_type = type('ModelType', (), {'value': 'detection'})()
            self.inference_params = type('InferenceParams', (), {
                'conf': 0.5,
                'iou': 0.4,
                'max_det': 3,
                'imgsz': 640,
                'device': 'cpu',
                'to_dict': lambda: {
                    'conf': 0.5,
                    'iou': 0.4,
                    'max_det': 3,
                    'imgsz': 640,
                    'device': 'cpu'
                }
            })()
    
    model_info = SimpleModelInfo(model_path)
    return DipperDetector(model_info)
