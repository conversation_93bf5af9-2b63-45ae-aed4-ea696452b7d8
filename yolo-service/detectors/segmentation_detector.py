"""
分割检测器

基于现有image_segmentation_service.py的优秀实现，提供图像分割功能
"""

import os
import sys
import time
import logging
from typing import Dict, List, Any, Union, Optional
from pathlib import Path
import numpy as np
import cv2

# 添加项目根目录到Python路径
project_root = Path(__file__).parent.parent.parent
sys.path.append(str(project_root))

from server.utils.logger import setup_logging
from ..core.base_detector import BaseDetector

# 初始化日志
setup_logging()
logger = logging.getLogger(__name__)


class SegmentationDetector(BaseDetector):
    """
    分割检测器
    
    功能：
    - 图像分割和目标检测
    - 计算分割面积和百分比
    - 提供详细的分割统计信息
    """
    
    def __init__(self, model_info):
        """
        初始化分割检测器
        
        Args:
            model_info: 模型配置信息
        """
        super().__init__(model_info)
        logger.info("分割检测器初始化完成")
    
    def _preprocess_image(self, image: np.ndarray) -> np.ndarray:
        """
        图像预处理
        
        Args:
            image: 输入图像
            
        Returns:
            np.ndarray: 处理后的图像
        """
        # 分割检测直接使用原始图像
        return image
    
    def _parse_results(self, results: Any, image_shape: tuple) -> List[Dict[str, Any]]:
        """
        解析分割推理结果
        
        Args:
            results: YOLO分割推理结果
            image_shape: 图像尺寸 (height, width, channels)
            
        Returns:
            List[Dict]: 解析后的检测结果
        """
        objects = []
        
        if len(results) == 0:
            return objects
        
        result = results[0]  # 获取第一张图片的结果
        
        # 检查是否有分割结果
        if not hasattr(result, 'masks') or result.masks is None:
            logger.warning("没有检测到分割结果")
            return objects
        
        masks = result.masks
        boxes = result.boxes
        
        if boxes is None or len(boxes) == 0:
            return objects
        
        # 计算总图像面积
        total_area = image_shape[0] * image_shape[1]
        
        # 处理每个检测到的对象
        for i in range(len(boxes)):
            box = boxes[i]
            bbox = box.xyxy[0].cpu().numpy().tolist()  # 边界框坐标
            cls_name = result.names[int(box.cls[0])]  # 获取类别名称
            conf = float(box.conf[0])  # 置信度
            
            # 获取对应的分割掩码
            mask_area = 0
            mask_data = None
            
            if i < len(masks.data):
                mask = masks.data[i].cpu().numpy()
                mask_area = int(np.sum(mask))
                mask_data = mask
            
            # 计算面积百分比
            area_percentage = (mask_area / total_area) * 100 if total_area > 0 else 0
            
            objects.append({
                "id": i,
                "class": cls_name,
                "confidence": conf,
                "bbox": bbox,
                "mask_area": mask_area,
                "area_percentage": area_percentage,
                "mask_data": mask_data
            })
        
        # 按面积排序
        objects.sort(key=lambda x: x["mask_area"], reverse=True)
        
        return objects
    
    def _calculate_segmentation_stats(self, objects: List[Dict], image_shape: tuple) -> Dict[str, Any]:
        """
        计算分割统计信息
        
        Args:
            objects: 检测到的对象列表
            image_shape: 图像尺寸
            
        Returns:
            Dict: 分割统计信息
        """
        total_area = image_shape[0] * image_shape[1]
        segmented_area = sum(obj["mask_area"] for obj in objects)
        percentage = (segmented_area / total_area) * 100 if total_area > 0 else 0
        
        # 按类别统计
        class_stats = {}
        for obj in objects:
            class_name = obj["class"]
            if class_name not in class_stats:
                class_stats[class_name] = {
                    "count": 0,
                    "total_area": 0,
                    "percentage": 0
                }
            
            class_stats[class_name]["count"] += 1
            class_stats[class_name]["total_area"] += obj["mask_area"]
            class_stats[class_name]["percentage"] += obj["area_percentage"]
        
        return {
            "total_area": total_area,
            "segmented_area": segmented_area,
            "percentage": percentage,
            "num_objects": len(objects),
            "class_stats": class_stats
        }
    
    def detect(self, image: Union[np.ndarray, str], 
               save_result: bool = False, 
               save_path: Optional[str] = None,
               save_annotated: bool = True) -> Dict[str, Any]:
        """
        执行分割检测（重写基类方法以支持分割特有参数）
        
        Args:
            image: 输入图像数组或图片路径
            save_result: 是否保存结果图像
            save_path: 指定保存路径
            save_annotated: 是否保存标注图像
            
        Returns:
            Dict: 检测结果
        """
        start_time = time.time()
        
        try:
            # 验证并加载图像
            image_array, input_path = self._validate_and_load_image(image)
            
            # 图像预处理
            processed_image = self._preprocess_image(image_array)
            
            # 执行推理
            results = self._run_inference(processed_image)
            
            # 解析结果
            objects = self._parse_results(results, processed_image.shape)
            
            # 计算分割统计信息
            stats = self._calculate_segmentation_stats(objects, processed_image.shape)
            
            # 构建响应
            response = {
                "success": True,
                "objects": objects,
                "objects_detail": objects,  # 兼容原有接口
                "total_area": stats["total_area"],
                "segmented_area": stats["segmented_area"],
                "percentage": stats["percentage"],
                "num_objects": stats["num_objects"],
                "class_stats": stats["class_stats"],
                "image_shape": processed_image.shape,
                "processing_time": round(time.time() - start_time, 3),
                "model_name": self.model_info.name
            }
            
            # 保存结果图像
            if save_result:
                if save_path is None:
                    save_path = self._generate_save_path(input_path)
                
                # 根据save_annotated参数决定保存的图像类型
                if save_annotated and len(results) > 0:
                    # 保存标注图像
                    annotated_img = results[0].plot()
                    cv2.imwrite(save_path, annotated_img)
                else:
                    # 保存原图
                    cv2.imwrite(save_path, processed_image)
                
                response["saved_path"] = save_path
            
            return response
            
        except Exception as e:
            logger.error(f"分割检测失败: {e}")
            return {
                "success": False,
                "error": str(e),
                "objects": [],
                "total_area": 0,
                "segmented_area": 0,
                "percentage": 0.0,
                "num_objects": 0,
                "processing_time": round(time.time() - start_time, 3),
                "model_name": self.model_info.name
            }
    
    def process_image(self, image_input: Union[np.ndarray, str],
                     save_path: str,
                     save_annotated: bool = True,
                     conf: float = 0.25,
                     iou: float = 0.7,
                     imgsz: int = 640) -> Dict[str, Any]:
        """
        处理图像（兼容原有接口）
        
        Args:
            image_input: 输入图像
            save_path: 保存路径
            save_annotated: 是否保存标注图像
            conf: 置信度阈值
            iou: IoU阈值
            imgsz: 图像尺寸
            
        Returns:
            Dict: 处理结果
        """
        # 临时更新推理参数
        original_params = self.inference_params.to_dict()
        self.inference_params.conf = conf
        self.inference_params.iou = iou
        self.inference_params.imgsz = imgsz
        
        try:
            result = self.detect(
                image=image_input,
                save_result=True,
                save_path=save_path,
                save_annotated=save_annotated
            )
            
            return result
            
        finally:
            # 恢复原始参数
            for key, value in original_params.items():
                setattr(self.inference_params, key, value)


def get_detector(model_path: str = "llms/models/yolo/best.pt"):
    """
    线程安全的检测器获取函数（兼容原有接口）
    
    Args:
        model_path: YOLO模型路径
        
    Returns:
        SegmentationDetector: 新的检测器实例
    """
    # 创建简单的模型信息对象
    class SimpleModelInfo:
        def __init__(self, path):
            self.name = "segmentation"
            self.path = path
            self.model_type = type('ModelType', (), {'value': 'segmentation'})()
            self.inference_params = type('InferenceParams', (), {
                'conf': 0.25,
                'iou': 0.7,
                'max_det': 100,
                'imgsz': 640,
                'device': 'cpu',
                'to_dict': lambda: {
                    'conf': 0.25,
                    'iou': 0.7,
                    'max_det': 100,
                    'imgsz': 640,
                    'device': 'cpu'
                }
            })()
    
    model_info = SimpleModelInfo(model_path)
    return SegmentationDetector(model_info)
