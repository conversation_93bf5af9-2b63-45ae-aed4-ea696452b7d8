"""
耙斗井检测器

基于现有yolo_api_server_dipper_shaft.py的优秀实现，提供耙斗井OBB检测功能
"""

import os
import sys
import logging
from typing import Dict, List, Any, Union
from pathlib import Path
import numpy as np
import cv2

# 添加项目根目录到Python路径
project_root = Path(__file__).parent.parent.parent
sys.path.append(str(project_root))

from server.utils.logger import setup_logging
from ..core.base_detector import BaseDetector

# 初始化日志
setup_logging()
logger = logging.getLogger(__name__)


class ShaftDetector(BaseDetector):
    """
    耙斗井检测器
    
    功能：
    - 使用OBB（Oriented Bounding Box）检测耙斗井设备
    - 提供旋转边界框信息
    - 支持多种检测结果格式
    """
    
    def __init__(self, model_info):
        """
        初始化耙斗井检测器
        
        Args:
            model_info: 模型配置信息
        """
        super().__init__(model_info)
        logger.info("耙斗井OBB检测器初始化完成")
    
    def _preprocess_image(self, image: np.ndarray) -> np.ndarray:
        """
        图像预处理
        
        Args:
            image: 输入图像
            
        Returns:
            np.ndarray: 处理后的图像
        """
        # 耙斗井检测直接使用原始图像
        return image
    
    def _parse_results(self, results: Any, image_shape: tuple) -> List[Dict[str, Any]]:
        """
        解析OBB推理结果

        Args:
            results: YOLO OBB推理结果
            image_shape: 图像尺寸 (height, width, channels)

        Returns:
            List[Dict]: 解析后的检测结果
        """
        devices = []

        if len(results) == 0:
            return devices

        result = results[0]  # 获取第一张图片的结果

        # 处理OBB检测框
        if result.obb is not None and len(result.obb) > 0:
            for i in range(len(result.obb)):
                obb = result.obb[i]
                cls_name = result.names[int(obb.cls[0])]  # 获取类别名称
                conf = float(obb.conf[0])  # 置信度

                # 获取完整的OBB信息
                obb_info = self._extract_obb_info(obb)

                # 计算水平边界框（用于兼容）
                bbox = self._calculate_horizontal_bbox(obb_info)

                # 计算几何属性
                geometry = self._calculate_geometry_properties(obb_info, image_shape)

                devices.append({
                    "id": i,
                    "device_id": i + 1,  # 兼容原有接口
                    "status": cls_name,
                    "confidence": conf,
                    "obb": obb_info,
                    "bbox": bbox,  # 水平边界框，用于兼容
                    "geometry": geometry  # 几何属性
                })

        # 按置信度排序
        devices.sort(key=lambda x: x["confidence"], reverse=True)

        return devices

    def _extract_obb_info(self, obb) -> Dict[str, Any]:
        """
        提取完整的OBB信息

        Args:
            obb: YOLO OBB对象

        Returns:
            Dict: OBB信息
        """
        obb_info = {}

        # 获取四个角点坐标
        if hasattr(obb, 'xyxyxyxy') and obb.xyxyxyxy is not None:
            xyxyxyxy = obb.xyxyxyxy[0].cpu().numpy().tolist()
            obb_info["xyxyxyxy"] = xyxyxyxy

            # 重新组织为四个点的坐标
            points = np.array(xyxyxyxy).reshape(-1, 2)
            obb_info["corners"] = [
                {"x": float(points[0][0]), "y": float(points[0][1])},  # 左上
                {"x": float(points[1][0]), "y": float(points[1][1])},  # 右上
                {"x": float(points[2][0]), "y": float(points[2][1])},  # 右下
                {"x": float(points[3][0]), "y": float(points[3][1])}   # 左下
            ]

        # 获取中心坐标+宽高+旋转角度
        if hasattr(obb, 'xywhr') and obb.xywhr is not None:
            xywhr = obb.xywhr[0].cpu().numpy().tolist()
            obb_info["xywhr"] = xywhr

            # 解析xywhr格式：[center_x, center_y, width, height, rotation]
            if len(xywhr) >= 5:
                obb_info["center"] = {"x": float(xywhr[0]), "y": float(xywhr[1])}
                obb_info["size"] = {"width": float(xywhr[2]), "height": float(xywhr[3])}
                obb_info["rotation_radians"] = float(xywhr[4])
                obb_info["rotation_degrees"] = float(np.degrees(xywhr[4]))

        # 获取置信度分布（如果有多个类别）
        if hasattr(obb, 'conf') and obb.conf is not None:
            obb_info["confidence"] = float(obb.conf[0])

        return obb_info

    def _calculate_horizontal_bbox(self, obb_info: Dict[str, Any]) -> List[float]:
        """
        计算水平边界框

        Args:
            obb_info: OBB信息

        Returns:
            List[float]: 水平边界框 [x_min, y_min, x_max, y_max]
        """
        if "xyxyxyxy" in obb_info:
            points = np.array(obb_info["xyxyxyxy"]).reshape(-1, 2)
            x_min, y_min = points.min(axis=0)
            x_max, y_max = points.max(axis=0)
            return [float(x_min), float(y_min), float(x_max), float(y_max)]
        elif "corners" in obb_info:
            x_coords = [corner["x"] for corner in obb_info["corners"]]
            y_coords = [corner["y"] for corner in obb_info["corners"]]
            return [min(x_coords), min(y_coords), max(x_coords), max(y_coords)]
        else:
            return [0.0, 0.0, 0.0, 0.0]

    def _calculate_geometry_properties(self, obb_info: Dict[str, Any],
                                     image_shape: tuple) -> Dict[str, Any]:
        """
        计算几何属性

        Args:
            obb_info: OBB信息
            image_shape: 图像尺寸

        Returns:
            Dict: 几何属性
        """
        geometry = {}

        if "size" in obb_info:
            width = obb_info["size"]["width"]
            height = obb_info["size"]["height"]

            # 计算面积
            geometry["area"] = width * height

            # 计算长宽比
            geometry["aspect_ratio"] = width / height if height > 0 else 0

            # 计算相对于图像的尺寸比例
            img_height, img_width = image_shape[:2]
            geometry["relative_size"] = {
                "width_ratio": width / img_width if img_width > 0 else 0,
                "height_ratio": height / img_height if img_height > 0 else 0,
                "area_ratio": (width * height) / (img_width * img_height) if (img_width * img_height) > 0 else 0
            }

        if "rotation_degrees" in obb_info:
            rotation = obb_info["rotation_degrees"]

            # 标准化角度到 [0, 360)
            normalized_rotation = rotation % 360
            geometry["normalized_rotation"] = normalized_rotation

            # 判断旋转类型
            if -15 <= rotation <= 15 or 345 <= rotation <= 360:
                geometry["rotation_type"] = "horizontal"
            elif 75 <= rotation <= 105:
                geometry["rotation_type"] = "vertical"
            elif 165 <= rotation <= 195:
                geometry["rotation_type"] = "inverted"
            elif 255 <= rotation <= 285:
                geometry["rotation_type"] = "vertical_inverted"
            else:
                geometry["rotation_type"] = "diagonal"

        if "center" in obb_info:
            center = obb_info["center"]
            img_height, img_width = image_shape[:2]

            # 计算中心点在图像中的相对位置
            geometry["center_position"] = {
                "x_ratio": center["x"] / img_width if img_width > 0 else 0,
                "y_ratio": center["y"] / img_height if img_height > 0 else 0
            }

            # 判断位置区域
            x_ratio = geometry["center_position"]["x_ratio"]
            y_ratio = geometry["center_position"]["y_ratio"]

            if x_ratio < 0.33:
                x_region = "left"
            elif x_ratio < 0.67:
                x_region = "center"
            else:
                x_region = "right"

            if y_ratio < 0.33:
                y_region = "top"
            elif y_ratio < 0.67:
                y_region = "middle"
            else:
                y_region = "bottom"

            geometry["position_region"] = f"{y_region}_{x_region}"

        return geometry
    
    def _add_custom_annotations(self, image: np.ndarray, devices: List[Dict]) -> np.ndarray:
        """
        添加自定义标注
        
        Args:
            image: 标注图像
            devices: 检测结果
            
        Returns:
            np.ndarray: 添加标注后的图像
        """
        annotated_img = image.copy()
        
        # 在图像上标注设备ID和旋转角度
        for device in devices:
            device_id = device["id"]
            obb_info = device.get("obb", {})
            
            # 如果有中心点信息，添加ID标注
            if "center" in obb_info:
                center_x, center_y = obb_info["center"]
                x, y = int(center_x), int(center_y - 20)
                
                # 添加设备ID标注
                cv2.putText(annotated_img, f"ID: {device_id}", (x, y), 
                           cv2.FONT_HERSHEY_SIMPLEX, 0.7, (0, 255, 0), 2)
                
                # 如果有旋转角度，添加角度标注
                if "rotation" in obb_info:
                    rotation = obb_info["rotation"]
                    angle_text = f"Angle: {rotation:.1f}°"
                    cv2.putText(annotated_img, angle_text, (x, y + 25), 
                               cv2.FONT_HERSHEY_SIMPLEX, 0.5, (255, 0, 0), 1)
        
        return annotated_img
    
    def detect_devices(self, image: Union[np.ndarray, str], 
                      save_result: bool = False) -> Dict[str, Any]:
        """
        检测耙斗井设备（兼容原有接口）
        
        Args:
            image: 输入图像数组或图片路径
            save_result: 是否保存识别结果图像
            
        Returns:
            Dict: 检测结果，包含devices列表和可选的image_path
        """
        result = self.detect(image, save_result=save_result)
        
        # 转换为原有接口格式
        response = {
            "devices": result.get("devices", [])
        }
        
        if "image_path" in result:
            response["image_path"] = result["image_path"]
        
        return response
    
    def get_rotation_summary(self, image: Union[np.ndarray, str]) -> Dict[str, Any]:
        """
        获取旋转角度摘要
        
        Args:
            image: 输入图像数组或图片路径
            
        Returns:
            Dict: 旋转角度摘要
        """
        result = self.detect_devices(image, save_result=False)
        devices = result.get("devices", [])
        
        rotations = []
        for device in devices:
            obb_info = device.get("obb", {})
            if "rotation" in obb_info:
                rotations.append(obb_info["rotation"])
        
        if not rotations:
            return {
                "total_devices": len(devices),
                "devices_with_rotation": 0,
                "average_rotation": 0,
                "rotation_range": [0, 0]
            }
        
        return {
            "total_devices": len(devices),
            "devices_with_rotation": len(rotations),
            "average_rotation": sum(rotations) / len(rotations),
            "rotation_range": [min(rotations), max(rotations)],
            "rotations": rotations
        }


def get_detector(model_path: str = "llms/models/yolo/best-dipper-shaft.pt"):
    """
    线程安全的检测器获取函数（兼容原有接口）
    
    Args:
        model_path: YOLO模型路径
        
    Returns:
        ShaftDetector: 新的检测器实例
    """
    # 创建简单的模型信息对象
    class SimpleModelInfo:
        def __init__(self, path):
            self.name = "shaft"
            self.path = path
            self.model_type = type('ModelType', (), {'value': 'obb'})()
            self.inference_params = type('InferenceParams', (), {
                'conf': 0.25,
                'iou': 0.2,
                'max_det': 5,
                'imgsz': 640,
                'device': 'cpu',
                'to_dict': lambda: {
                    'conf': 0.25,
                    'iou': 0.2,
                    'max_det': 5,
                    'imgsz': 640,
                    'device': 'cpu'
                }
            })()
    
    model_info = SimpleModelInfo(model_path)
    return ShaftDetector(model_info)
