"""
YOLO检测器模块

包含各种专用的检测器实现：
- DipperDetector: 耙斗检测器
- FilterDetector: 滤池检测器
- ShaftDetector: 耙斗井检测器
- SegmentationDetector: 分割检测器
"""

from .dipper_detector import DipperDetector
from .filter_detector import FilterDetector
from .shaft_detector import ShaftDetector
from .segmentation_detector import SegmentationDetector

__all__ = [
    "DipperDetector",
    "FilterDetector",
    "ShaftDetector",
    "SegmentationDetector"
]
