"""
滤池检测器

基于现有yolo_api_server_filter.py的优秀实现，提供滤池故障检测功能
"""

import os
import sys
import logging
from typing import Dict, List, Any, Union, Optional
from pathlib import Path
import numpy as np
import cv2

# 添加项目根目录到Python路径
project_root = Path(__file__).parent.parent.parent
sys.path.append(str(project_root))

from server.utils.logger import setup_logging
from ..core.base_detector import BaseDetector

# 初始化日志
setup_logging()
logger = logging.getLogger(__name__)


class FilterDetector(BaseDetector):
    """
    滤池检测器
    
    功能：
    - 检测滤池设备的故障状态
    - 支持多种故障类型识别
    - 提供详细的检测结果和标注图像
    """
    
    def __init__(self, model_info):
        """
        初始化滤池检测器
        
        Args:
            model_info: 模型配置信息
        """
        super().__init__(model_info)
        logger.info("滤池检测器初始化完成")
    
    def _preprocess_image(self, image: np.ndarray) -> np.ndarray:
        """
        图像预处理
        
        Args:
            image: 输入图像
            
        Returns:
            np.ndarray: 处理后的图像
        """
        # 滤池检测直接使用原始图像，不进行额外预处理
        return image
    
    def _parse_results(self, results: Any, image_shape: tuple) -> List[Dict[str, Any]]:
        """
        解析推理结果
        
        Args:
            results: YOLO推理结果
            image_shape: 图像尺寸 (height, width, channels)
            
        Returns:
            List[Dict]: 解析后的检测结果
        """
        devices = []
        
        if len(results) == 0:
            return devices
        
        result = results[0]  # 获取第一张图片的结果
        boxes = result.boxes
        
        if boxes is None or len(boxes) == 0:
            return devices
        
        # 处理检测框
        for i in range(len(boxes)):
            box = boxes[i]
            bbox = box.xyxy[0].cpu().numpy().tolist()  # 边界框坐标
            cls_name = result.names[int(box.cls[0])]  # 获取类别名称
            conf = float(box.conf[0])  # 置信度
            
            devices.append({
                "id": i,
                "status": cls_name,
                "confidence": conf,
                "bbox": bbox,
                "area": (bbox[2] - bbox[0]) * (bbox[3] - bbox[1])  # 计算检测框面积
            })
        
        # 按置信度排序
        devices.sort(key=lambda x: x["confidence"], reverse=True)
        
        return devices
    
    def _add_custom_annotations(self, image: np.ndarray, devices: List[Dict]) -> np.ndarray:
        """
        添加自定义标注
        
        Args:
            image: 标注图像
            devices: 检测结果
            
        Returns:
            np.ndarray: 添加标注后的图像
        """
        # 滤池检测使用YOLO自带的标注，不添加额外标注
        # 如果需要自定义标注，可以在这里实现
        return image
    
    def detect_devices(self, image: Union[np.ndarray, str], 
                      save_result: bool = False, 
                      output_path: Optional[str] = None) -> Dict[str, Any]:
        """
        检测滤池设备状态（兼容原有接口）
        
        Args:
            image: 输入图像数组或图片路径
            save_result: 是否保存识别结果图像
            output_path: 指定保存路径，如果为None则保存到tests文件夹
            
        Returns:
            Dict: 检测结果，包含devices列表、annotated_image和可选的image_path
        """
        # 如果指定了输出路径，使用指定路径
        save_path = output_path if output_path else None
        
        result = self.detect(image, save_result=save_result, save_path=save_path)
        
        # 获取标注图像
        annotated_image = None
        if result.get("success", False):
            try:
                # 重新运行推理以获取标注图像
                image_array, _ = self._validate_and_load_image(image)
                processed_image = self._preprocess_image(image_array)
                inference_results = self._run_inference(processed_image)
                
                if len(inference_results) > 0:
                    annotated_image = inference_results[0].plot()
                
            except Exception as e:
                logger.warning(f"生成标注图像失败: {e}")
                annotated_image = None
        
        # 转换为原有接口格式
        response = {
            "devices": result.get("devices", []),
            "annotated_image": annotated_image
        }
        
        if "image_path" in result:
            response["image_path"] = result["image_path"]
        
        return response
    
    def get_malfunction_count(self, image: Union[np.ndarray, str]) -> int:
        """
        获取故障数量
        
        Args:
            image: 输入图像数组或图片路径
            
        Returns:
            int: 检测到的故障数量
        """
        result = self.detect_devices(image, save_result=False)
        devices = result.get("devices", [])
        
        # 统计故障设备数量（状态为"malfunction"的设备）
        malfunction_count = sum(1 for device in devices if device.get("status") == "malfunction")
        
        return malfunction_count
    
    def get_device_summary(self, image: Union[np.ndarray, str]) -> Dict[str, Any]:
        """
        获取设备状态摘要
        
        Args:
            image: 输入图像数组或图片路径
            
        Returns:
            Dict: 设备状态摘要
        """
        result = self.detect_devices(image, save_result=False)
        devices = result.get("devices", [])
        
        # 统计各种状态的设备数量
        status_count = {}
        total_devices = len(devices)
        
        for device in devices:
            status = device.get("status", "unknown")
            status_count[status] = status_count.get(status, 0) + 1
        
        return {
            "total_devices": total_devices,
            "status_count": status_count,
            "malfunction_count": status_count.get("malfunction", 0),
            "normal_count": total_devices - status_count.get("malfunction", 0)
        }


def get_detector(model_path: str = "llms/models/yolo/best-filter.pt"):
    """
    线程安全的检测器获取函数（兼容原有接口）
    
    Args:
        model_path: YOLO模型路径
        
    Returns:
        FilterDetector: 新的检测器实例
    """
    # 创建简单的模型信息对象
    class SimpleModelInfo:
        def __init__(self, path):
            self.name = "filter"
            self.path = path
            self.model_type = type('ModelType', (), {'value': 'detection'})()
            self.inference_params = type('InferenceParams', (), {
                'conf': 0.6,
                'iou': 0.6,
                'max_det': 10,
                'imgsz': 640,
                'device': 'cpu',
                'to_dict': lambda: {
                    'conf': 0.6,
                    'iou': 0.6,
                    'max_det': 10,
                    'imgsz': 640,
                    'device': 'cpu'
                }
            })()
    
    model_info = SimpleModelInfo(model_path)
    return FilterDetector(model_info)
