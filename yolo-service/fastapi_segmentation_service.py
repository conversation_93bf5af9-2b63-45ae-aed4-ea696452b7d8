#!/usr/bin/env python3
"""
FastAPI图像分割服务
基于YOLO的图像分割Web API服务
"""

import os
import io
import uuid
import logging
from pathlib import Path
from typing import List, Optional, Union
import numpy as np
import cv2
from PIL import Image
from fastapi import FastAPI, File, UploadFile, HTTPException, Form, BackgroundTasks
from fastapi.responses import FileResponse, JSONResponse
from fastapi.middleware.cors import CORSMiddleware
from pydantic import BaseModel, Field
import uvicorn

from image_segmentation_service import ImageSegmentationService

# 配置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

# 创建FastAPI应用
app = FastAPI(
    title="图像分割服务API",
    description="基于YOLO的图像分割Web API服务",
    version="1.0.0",
    docs_url="/docs",
    redoc_url="/redoc"
)

# 添加CORS中间件
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],  # 生产环境中应该限制具体域名
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# 全局配置
class Config:
    MODEL_PATH = "runs/segment/water_segmentation1/weights/best.pt"
    UPLOAD_DIR = "uploads"
    OUTPUT_DIR = "outputs"
    MAX_WORKERS = 4
    MAX_FILE_SIZE = 10 * 1024 * 1024  # 10MB
    ALLOWED_EXTENSIONS = {".jpg", ".jpeg", ".png", ".bmp", ".tiff", ".webp"}

config = Config()

# 创建必要的目录
os.makedirs(config.UPLOAD_DIR, exist_ok=True)
os.makedirs(config.OUTPUT_DIR, exist_ok=True)

# 全局服务实例
segmentation_service = None

# Pydantic模型
class SegmentationResult(BaseModel):
    """分割结果模型"""
    success: bool
    message: str = ""
    saved_path: Optional[str] = None
    download_url: Optional[str] = None
    total_area: int = 0
    segmented_area: int = 0
    percentage: float = 0.0
    num_objects: int = 0
    objects_detail: List[dict] = []
    image_shape: Optional[tuple] = None
    processing_time: Optional[float] = None

class BatchSegmentationResult(BaseModel):
    """批量分割结果模型"""
    success: bool
    message: str = ""
    total_images: int = 0
    successful_images: int = 0
    failed_images: int = 0
    results: List[SegmentationResult] = []
    statistics: Optional[dict] = None
    processing_time: Optional[float] = None

class HealthResponse(BaseModel):
    """健康检查响应模型"""
    status: str
    model_loaded: bool
    service_version: str = "1.0.0"

# 启动事件
@app.on_event("startup")
async def startup_event():
    """应用启动时初始化服务"""
    global segmentation_service
    try:
        if not os.path.exists(config.MODEL_PATH):
            logger.error(f"模型文件不存在: {config.MODEL_PATH}")
            raise FileNotFoundError(f"模型文件不存在: {config.MODEL_PATH}")
        
        segmentation_service = ImageSegmentationService(
            model_path=config.MODEL_PATH,
            max_workers=config.MAX_WORKERS
        )
        logger.info("图像分割服务启动成功")
    except Exception as e:
        logger.error(f"服务启动失败: {e}")
        raise

# 关闭事件
@app.on_event("shutdown")
async def shutdown_event():
    """应用关闭时清理资源"""
    logger.info("图像分割服务正在关闭...")

# 工具函数
def validateImageFile(file: UploadFile) -> bool:
    """验证上传的图像文件"""
    if not file.filename:
        return False
    
    # 检查文件扩展名
    file_ext = Path(file.filename).suffix.lower()
    if file_ext not in config.ALLOWED_EXTENSIONS:
        return False
    
    # 检查文件大小
    if hasattr(file, 'size') and file.size > config.MAX_FILE_SIZE:
        return False
    
    return True

def saveUploadedFile(file: UploadFile) -> str:
    """保存上传的文件"""
    try:
        # 生成唯一文件名
        file_ext = Path(file.filename).suffix.lower()
        unique_filename = f"{uuid.uuid4()}{file_ext}"
        file_path = Path(config.UPLOAD_DIR) / unique_filename
        
        # 保存文件
        with open(file_path, "wb") as buffer:
            content = file.file.read()
            buffer.write(content)
        
        logger.info(f"文件已保存: {file_path}")
        return str(file_path)
    
    except Exception as e:
        logger.error(f"保存文件失败: {e}")
        raise HTTPException(status_code=500, detail=f"保存文件失败: {str(e)}")

def generateOutputPath(input_filename: str) -> str:
    """生成输出文件路径"""
    base_name = Path(input_filename).stem
    unique_id = str(uuid.uuid4())[:8]
    output_filename = f"{base_name}_{unique_id}_segmented.jpg"
    return str(Path(config.OUTPUT_DIR) / output_filename)

def cleanupFile(file_path: str):
    """清理临时文件"""
    try:
        if os.path.exists(file_path):
            os.remove(file_path)
            logger.debug(f"已清理文件: {file_path}")
    except Exception as e:
        logger.warning(f"清理文件失败 {file_path}: {e}")

# API端点
@app.get("/", response_model=dict)
async def root():
    """根端点"""
    return {
        "message": "图像分割服务API",
        "version": "1.0.0",
        "docs": "/docs",
        "health": "/health"
    }

@app.get("/health", response_model=HealthResponse)
async def health_check():
    """健康检查端点"""
    model_loaded = segmentation_service is not None
    return HealthResponse(
        status="healthy" if model_loaded else "unhealthy",
        model_loaded=model_loaded
    )

@app.post("/segment/upload", response_model=SegmentationResult)
async def segmentUploadedImage(
    background_tasks: BackgroundTasks,
    file: UploadFile = File(..., description="要分割的图像文件"),
    save_annotated: bool = Form(True, description="是否保存标注图像"),
    conf: float = Form(0.25, ge=0.0, le=1.0, description="置信度阈值"),
    iou: float = Form(0.7, ge=0.0, le=1.0, description="IoU阈值"),
    imgsz: int = Form(640, ge=320, le=1280, description="推理图像大小")
):
    """
    上传图像文件进行分割
    
    - **file**: 图像文件 (支持 jpg, jpeg, png, bmp, tiff, webp)
    - **save_annotated**: 是否保存标注图像 (True=标注图, False=原图)
    - **conf**: 置信度阈值 (0.0-1.0)
    - **iou**: IoU阈值 (0.0-1.0) 
    - **imgsz**: 推理图像大小 (320-1280)
    """
    import time
    start_time = time.time()
    
    try:
        # 验证文件
        if not validateImageFile(file):
            raise HTTPException(
                status_code=400, 
                detail="无效的图像文件。支持的格式: jpg, jpeg, png, bmp, tiff, webp"
            )
        
        # 保存上传的文件
        input_path = saveUploadedFile(file)
        
        # 生成输出路径
        output_path = generateOutputPath(file.filename)
        
        # 执行分割
        result = segmentation_service.processImage(
            image_input=input_path,
            save_path=output_path,
            save_annotated=save_annotated,
            conf=conf,
            iou=iou,
            imgsz=imgsz
        )
        
        # 计算处理时间
        processing_time = time.time() - start_time
        
        # 添加清理任务
        background_tasks.add_task(cleanupFile, input_path)
        
        if result["success"]:
            # 生成下载URL - 使用实际保存的文件名
            if result["saved_path"]:
                actual_filename = Path(result["saved_path"]).name
                download_url = f"/download-safe/{actual_filename}"
            else:
                download_url = None

            return SegmentationResult(
                success=True,
                message="图像分割成功",
                saved_path=result["saved_path"],
                download_url=download_url,
                total_area=result["total_area"],
                segmented_area=result["segmented_area"],
                percentage=result["percentage"],
                num_objects=result["num_objects"],
                objects_detail=result["objects_detail"],
                image_shape=result["image_shape"],
                processing_time=round(processing_time, 2)
            )
        else:
            raise HTTPException(
                status_code=500,
                detail=f"图像分割失败: {result.get('error', '未知错误')}"
            )
    
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"处理图像时出错: {e}")
        raise HTTPException(status_code=500, detail=f"处理图像时出错: {str(e)}")

@app.get("/download/{filename}")
async def downloadResult(filename: str):
    """
    下载处理结果文件

    - **filename**: 文件名
    """
    file_path = Path(config.OUTPUT_DIR) / filename

    if not file_path.exists():
        raise HTTPException(status_code=404, detail="文件不存在")

    # 清理文件名，移除特殊字符
    safe_filename = filename.replace("%", "percent")

    return FileResponse(
        path=str(file_path),
        filename=safe_filename,
        media_type="image/jpeg",
        headers={
            "Content-Disposition": f"attachment; filename=\"{safe_filename}\"",
            "Access-Control-Allow-Origin": "*",
            "Access-Control-Allow-Methods": "GET",
            "Access-Control-Allow-Headers": "*",
            "Cache-Control": "no-cache"
        }
    )

@app.get("/download-safe/{filename}")
async def downloadResultSafe(filename: str):
    """
    安全下载处理结果文件（解决浏览器安全策略问题）

    - **filename**: 文件名
    """
    file_path = Path(config.OUTPUT_DIR) / filename

    if not file_path.exists():
        raise HTTPException(status_code=404, detail="文件不存在")

    # 读取文件内容
    try:
        with open(file_path, "rb") as f:
            file_content = f.read()

        # 生成安全的文件名
        safe_filename = filename.replace("%", "percent").replace(" ", "_")

        # 返回带有安全头的响应
        from fastapi.responses import Response

        return Response(
            content=file_content,
            media_type="image/jpeg",
            headers={
                "Content-Disposition": f"attachment; filename=\"{safe_filename}\"",
                "Content-Type": "image/jpeg",
                "Access-Control-Allow-Origin": "*",
                "Access-Control-Allow-Methods": "GET, POST, OPTIONS",
                "Access-Control-Allow-Headers": "*",
                "Cache-Control": "no-cache, no-store, must-revalidate",
                "Pragma": "no-cache",
                "Expires": "0",
                "X-Content-Type-Options": "nosniff",
                "X-Frame-Options": "DENY",
                "Content-Security-Policy": "default-src 'none'"
            }
        )
    except Exception as e:
        logger.error(f"读取文件失败: {e}")
        raise HTTPException(status_code=500, detail="文件读取失败")

@app.post("/segment/batch", response_model=BatchSegmentationResult)
async def segmentBatchImages(
    background_tasks: BackgroundTasks,
    files: List[UploadFile] = File(..., description="要分割的图像文件列表"),
    save_annotated: bool = Form(True, description="是否保存标注图像"),
    conf: float = Form(0.25, ge=0.0, le=1.0, description="置信度阈值"),
    iou: float = Form(0.7, ge=0.0, le=1.0, description="IoU阈值"),
    imgsz: int = Form(640, ge=320, le=1280, description="推理图像大小")
):
    """
    批量上传图像文件进行分割

    - **files**: 图像文件列表
    - **save_annotated**: 是否保存标注图像
    - **conf**: 置信度阈值
    - **iou**: IoU阈值
    - **imgsz**: 推理图像大小
    """
    import time
    start_time = time.time()

    try:
        if len(files) == 0:
            raise HTTPException(status_code=400, detail="请至少上传一个文件")

        if len(files) > 20:  # 限制批量处理数量
            raise HTTPException(status_code=400, detail="批量处理最多支持20个文件")

        # 验证所有文件
        for file in files:
            if not validateImageFile(file):
                raise HTTPException(
                    status_code=400,
                    detail=f"文件 {file.filename} 格式不支持"
                )

        # 保存所有上传的文件
        input_paths = []
        for file in files:
            input_path = saveUploadedFile(file)
            input_paths.append(input_path)

        # 生成输出目录
        batch_id = str(uuid.uuid4())[:8]
        batch_output_dir = Path(config.OUTPUT_DIR) / f"batch_{batch_id}"
        batch_output_dir.mkdir(exist_ok=True)

        # 执行批量分割
        results = segmentation_service.processBatchImages(
            image_inputs=input_paths,
            save_dir=str(batch_output_dir),
            save_annotated=save_annotated,
            conf=conf,
            iou=iou,
            imgsz=imgsz
        )

        # 获取统计信息
        statistics = segmentation_service.getBatchStatistics(results)

        # 计算处理时间
        processing_time = time.time() - start_time

        # 构建结果
        api_results = []
        for i, result in enumerate(results):
            if result["success"] and result["saved_path"]:
                filename = Path(result["saved_path"]).name
                download_url = f"/download/batch_{batch_id}/{filename}"
            else:
                download_url = None

            api_result = SegmentationResult(
                success=result["success"],
                message="处理成功" if result["success"] else result.get("error", "处理失败"),
                saved_path=result.get("saved_path"),
                download_url=download_url,
                total_area=result.get("total_area", 0),
                segmented_area=result.get("segmented_area", 0),
                percentage=result.get("percentage", 0.0),
                num_objects=result.get("num_objects", 0),
                objects_detail=result.get("objects_detail", []),
                image_shape=result.get("image_shape")
            )
            api_results.append(api_result)

        # 添加清理任务
        for input_path in input_paths:
            background_tasks.add_task(cleanupFile, input_path)

        return BatchSegmentationResult(
            success=True,
            message=f"批量处理完成，成功处理 {statistics['successful_images']}/{statistics['total_images']} 张图像",
            total_images=statistics["total_images"],
            successful_images=statistics["successful_images"],
            failed_images=statistics["failed_images"],
            results=api_results,
            statistics=statistics,
            processing_time=round(processing_time, 2)
        )

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"批量处理图像时出错: {e}")
        raise HTTPException(status_code=500, detail=f"批量处理图像时出错: {str(e)}")

@app.get("/download/batch_{batch_id}/{filename}")
async def downloadBatchResult(batch_id: str, filename: str):
    """
    下载批量处理结果文件

    - **batch_id**: 批次ID
    - **filename**: 文件名
    """
    file_path = Path(config.OUTPUT_DIR) / f"batch_{batch_id}" / filename

    if not file_path.exists():
        raise HTTPException(status_code=404, detail="文件不存在")

    # 清理文件名，移除特殊字符
    safe_filename = filename.replace("%", "percent")

    return FileResponse(
        path=str(file_path),
        filename=safe_filename,
        media_type="image/jpeg",
        headers={
            "Content-Disposition": f"attachment; filename=\"{safe_filename}\"",
            "Access-Control-Allow-Origin": "*",
            "Access-Control-Allow-Methods": "GET",
            "Access-Control-Allow-Headers": "*",
            "Cache-Control": "no-cache"
        }
    )

@app.post("/segment/base64", response_model=SegmentationResult)
async def segmentBase64Image(
    image_data: str = Form(..., description="Base64编码的图像数据"),
    save_annotated: bool = Form(True, description="是否保存标注图像"),
    conf: float = Form(0.25, ge=0.0, le=1.0, description="置信度阈值"),
    iou: float = Form(0.7, ge=0.0, le=1.0, description="IoU阈值"),
    imgsz: int = Form(640, ge=320, le=1280, description="推理图像大小")
):
    """
    处理Base64编码的图像数据

    - **image_data**: Base64编码的图像数据
    - **save_annotated**: 是否保存标注图像
    - **conf**: 置信度阈值
    - **iou**: IoU阈值
    - **imgsz**: 推理图像大小
    """
    import time
    import base64
    start_time = time.time()

    try:
        # 解码Base64数据
        try:
            # 移除可能的数据URL前缀
            if image_data.startswith('data:image'):
                image_data = image_data.split(',')[1]

            image_bytes = base64.b64decode(image_data)

            # 转换为numpy数组
            image_array = np.frombuffer(image_bytes, dtype=np.uint8)
            image = cv2.imdecode(image_array, cv2.IMREAD_COLOR)

            if image is None:
                raise ValueError("无法解码图像数据")

        except Exception as e:
            raise HTTPException(status_code=400, detail=f"无效的Base64图像数据: {str(e)}")

        # 生成输出路径
        output_path = generateOutputPath("base64_image")

        # 执行分割
        result = segmentation_service.processImage(
            image_input=image,
            save_path=output_path,
            save_annotated=save_annotated,
            conf=conf,
            iou=iou,
            imgsz=imgsz
        )

        # 计算处理时间
        processing_time = time.time() - start_time

        if result["success"]:
            # 生成下载URL - 使用实际保存的文件名
            if result["saved_path"]:
                actual_filename = Path(result["saved_path"]).name
                download_url = f"/download-safe/{actual_filename}"
            else:
                download_url = None

            return SegmentationResult(
                success=True,
                message="图像分割成功",
                saved_path=result["saved_path"],
                download_url=download_url,
                total_area=result["total_area"],
                segmented_area=result["segmented_area"],
                percentage=result["percentage"],
                num_objects=result["num_objects"],
                objects_detail=result["objects_detail"],
                image_shape=result["image_shape"],
                processing_time=round(processing_time, 2)
            )
        else:
            raise HTTPException(
                status_code=500,
                detail=f"图像分割失败: {result.get('error', '未知错误')}"
            )

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"处理Base64图像时出错: {e}")
        raise HTTPException(status_code=500, detail=f"处理Base64图像时出错: {str(e)}")

@app.get("/config", response_model=dict)
async def getConfig():
    """
    获取服务配置信息
    """
    return {
        "model_path": config.MODEL_PATH,
        "max_workers": config.MAX_WORKERS,
        "max_file_size_mb": config.MAX_FILE_SIZE // (1024 * 1024),
        "allowed_extensions": list(config.ALLOWED_EXTENSIONS),
        "upload_dir": config.UPLOAD_DIR,
        "output_dir": config.OUTPUT_DIR
    }

@app.delete("/cleanup", response_model=dict)
async def cleanupOldFiles(
    days: int = Form(7, ge=1, le=30, description="清理多少天前的文件")
):
    """
    清理旧的输出文件

    - **days**: 清理多少天前的文件 (1-30天)
    """
    try:
        import time
        from datetime import datetime, timedelta

        cutoff_time = time.time() - (days * 24 * 60 * 60)
        cleaned_count = 0

        # 清理输出目录
        for file_path in Path(config.OUTPUT_DIR).rglob("*"):
            if file_path.is_file():
                if file_path.stat().st_mtime < cutoff_time:
                    try:
                        file_path.unlink()
                        cleaned_count += 1
                    except Exception as e:
                        logger.warning(f"删除文件失败 {file_path}: {e}")

        # 清理空目录
        for dir_path in Path(config.OUTPUT_DIR).rglob("*"):
            if dir_path.is_dir() and not any(dir_path.iterdir()):
                try:
                    dir_path.rmdir()
                except Exception as e:
                    logger.warning(f"删除空目录失败 {dir_path}: {e}")

        return {
            "success": True,
            "message": f"已清理 {cleaned_count} 个文件",
            "cleaned_files": cleaned_count,
            "cutoff_days": days
        }

    except Exception as e:
        logger.error(f"清理文件时出错: {e}")
        raise HTTPException(status_code=500, detail=f"清理文件时出错: {str(e)}")

# 异常处理器
@app.exception_handler(404)
async def notFoundHandler(request, exc):
    """404错误处理器"""
    return JSONResponse(
        status_code=404,
        content={"detail": "请求的资源不存在", "path": str(request.url)}
    )

@app.exception_handler(500)
async def internalServerErrorHandler(request, exc):
    """500错误处理器"""
    logger.error(f"内部服务器错误: {exc}")
    return JSONResponse(
        status_code=500,
        content={"detail": "内部服务器错误，请稍后重试"}
    )

def main():
    """
    启动FastAPI服务
    """
    import argparse

    parser = argparse.ArgumentParser(description="图像分割FastAPI服务")
    parser.add_argument("--host", default="0.0.0.0", help="服务器主机地址")
    parser.add_argument("--port", type=int, default=8008, help="服务器端口")
    parser.add_argument("--model-path", default=config.MODEL_PATH, help="YOLO模型路径")
    parser.add_argument("--max-workers", type=int, default=config.MAX_WORKERS, help="最大工作线程数")
    parser.add_argument("--reload", action="store_true", help="开发模式，自动重载")
    parser.add_argument("--log-level", default="info", choices=["debug", "info", "warning", "error"], help="日志级别")

    args = parser.parse_args()

    # 更新配置
    config.MODEL_PATH = args.model_path
    config.MAX_WORKERS = args.max_workers

    # 验证模型文件
    if not os.path.exists(config.MODEL_PATH):
        logger.error(f"模型文件不存在: {config.MODEL_PATH}")
        print(f"错误: 模型文件不存在 {config.MODEL_PATH}")
        print("请确保模型文件路径正确，或使用 --model-path 参数指定正确路径")
        return

    print("=" * 60)
    print("🚀 图像分割FastAPI服务")
    print("=" * 60)
    print(f"📍 服务地址: http://{args.host}:{args.port}")
    print(f"📚 API文档: http://{args.host}:{args.port}/docs")
    print(f"🔄 ReDoc文档: http://{args.host}:{args.port}/redoc")
    print(f"🤖 模型路径: {config.MODEL_PATH}")
    print(f"🧵 最大线程数: {config.MAX_WORKERS}")
    print(f"📁 上传目录: {config.UPLOAD_DIR}")
    print(f"📁 输出目录: {config.OUTPUT_DIR}")
    print("=" * 60)

    # 启动服务
    uvicorn.run(
        "fastapi_segmentation_service:app",
        host=args.host,
        port=args.port,
        reload=args.reload,
        log_level=args.log_level,
        access_log=True
    )

if __name__ == "__main__":
    main()
