"""
YOLO模型管理器

处理模型的预加载、缓存和线程安全访问
"""

import os
import sys
import time
import logging
import threading
from typing import Dict, Optional, Any, List
from pathlib import Path
import weakref
from ultralytics import YOLO

# 添加项目根目录到Python路径
project_root = Path(__file__).parent.parent.parent
sys.path.append(str(project_root))

from server.utils.logger import setup_logging

# 初始化日志
setup_logging()
logger = logging.getLogger(__name__)


class ModelInstance:
    """模型实例包装器"""
    
    def __init__(self, model: YOLO, model_name: str, model_path: str):
        self.model = model
        self.model_name = model_name
        self.model_path = model_path
        self.created_at = time.time()
        self.last_used = time.time()
        self.use_count = 0
        self._lock = threading.RLock()
    
    def get_model(self) -> YOLO:
        """获取模型实例"""
        with self._lock:
            self.last_used = time.time()
            self.use_count += 1
            return self.model
    
    def get_stats(self) -> Dict[str, Any]:
        """获取模型统计信息"""
        with self._lock:
            return {
                "model_name": self.model_name,
                "model_path": self.model_path,
                "created_at": self.created_at,
                "last_used": self.last_used,
                "use_count": self.use_count,
                "age_seconds": time.time() - self.created_at,
                "idle_seconds": time.time() - self.last_used
            }


class ModelManager:
    """
    YOLO模型管理器
    
    功能：
    - 模型预加载和缓存
    - 线程安全的模型访问
    - 模型生命周期管理
    - 内存使用优化
    """
    
    _instance = None
    _lock = threading.Lock()
    
    def __new__(cls):
        """单例模式"""
        if cls._instance is None:
            with cls._lock:
                if cls._instance is None:
                    cls._instance = super().__new__(cls)
                    cls._instance._initialized = False
        return cls._instance
    
    def __init__(self):
        """初始化模型管理器"""
        if self._initialized:
            return
        
        self._models: Dict[str, ModelInstance] = {}  # 模型缓存
        self._model_configs: Dict[str, Dict] = {}    # 模型配置
        self._lock = threading.RLock()               # 可重入锁
        self._max_cache_size = 5                     # 最大缓存模型数
        self._max_idle_time = 3600                   # 最大空闲时间（秒）
        
        logger.info("模型管理器初始化完成")
        self._initialized = True

    def _map_task_type(self, task_type: str) -> str:
        """
        映射任务类型到YOLO支持的格式

        Args:
            task_type: 输入的任务类型

        Returns:
            str: 映射后的任务类型
        """
        task_mapping = {
            'detection': 'detect',
            'segmentation': 'segment',
            'obb': 'obb',
            'detect': 'detect',
            'segment': 'segment'
        }

        mapped_type = task_mapping.get(task_type.lower(), task_type)
        logger.debug(f"任务类型映射: {task_type} -> {mapped_type}")
        return mapped_type
    
    def register_model(self, model_name: str, model_path: str,
                      task_type: str = "detect", **kwargs) -> None:
        """
        注册模型配置

        Args:
            model_name: 模型名称
            model_path: 模型文件路径
            task_type: 任务类型 (detect, obb, segment, segmentation)
            **kwargs: 其他配置参数
        """
        with self._lock:
            if not os.path.exists(model_path):
                raise FileNotFoundError(f"模型文件不存在: {model_path}")

            # 映射任务类型
            mapped_task_type = self._map_task_type(task_type)

            self._model_configs[model_name] = {
                "path": model_path,
                "task_type": mapped_task_type,
                **kwargs
            }

            logger.info(f"模型已注册: {model_name} -> {model_path} (任务类型: {mapped_task_type})")
    
    def preload_model(self, model_name: str) -> bool:
        """
        预加载模型
        
        Args:
            model_name: 模型名称
            
        Returns:
            bool: 加载是否成功
        """
        with self._lock:
            if model_name in self._models:
                logger.info(f"模型已加载: {model_name}")
                return True
            
            if model_name not in self._model_configs:
                logger.error(f"未注册的模型: {model_name}")
                return False
            
            try:
                config = self._model_configs[model_name]
                model_path = config["path"]
                task_type = config.get("task_type", "detect")
                
                logger.info(f"开始加载模型: {model_name}")
                start_time = time.time()
                
                # 创建YOLO模型实例
                model = YOLO(model_path, task=task_type)
                
                # 包装模型实例
                model_instance = ModelInstance(model, model_name, model_path)
                
                # 检查缓存大小
                self._check_cache_size()
                
                # 添加到缓存
                self._models[model_name] = model_instance
                
                load_time = time.time() - start_time
                logger.info(f"模型加载成功: {model_name}, 耗时: {load_time:.2f}秒")
                
                return True
                
            except Exception as e:
                logger.error(f"模型加载失败: {model_name}, 错误: {e}")
                return False
    
    def get_model(self, model_name: str, auto_load: bool = True) -> Optional[YOLO]:
        """
        获取模型实例
        
        Args:
            model_name: 模型名称
            auto_load: 是否自动加载模型
            
        Returns:
            YOLO模型实例或None
        """
        with self._lock:
            # 如果模型已缓存，直接返回
            if model_name in self._models:
                return self._models[model_name].get_model()
            
            # 如果允许自动加载且模型已注册
            if auto_load and model_name in self._model_configs:
                if self.preload_model(model_name):
                    return self._models[model_name].get_model()
            
            logger.warning(f"模型不可用: {model_name}")
            return None
    
    def unload_model(self, model_name: str) -> bool:
        """
        卸载模型
        
        Args:
            model_name: 模型名称
            
        Returns:
            bool: 卸载是否成功
        """
        with self._lock:
            if model_name not in self._models:
                logger.warning(f"模型未加载: {model_name}")
                return True
            
            try:
                model_instance = self._models[model_name]
                
                # 清理模型资源
                del model_instance.model
                del self._models[model_name]
                
                logger.info(f"模型已卸载: {model_name}")
                return True
                
            except Exception as e:
                logger.error(f"模型卸载失败: {model_name}, 错误: {e}")
                return False
    
    def is_model_loaded(self, model_name: str) -> bool:
        """
        检查模型是否已加载
        
        Args:
            model_name: 模型名称
            
        Returns:
            bool: 模型是否已加载
        """
        with self._lock:
            return model_name in self._models
    
    def get_loaded_models(self) -> List[str]:
        """
        获取已加载的模型列表
        
        Returns:
            List[str]: 已加载的模型名称列表
        """
        with self._lock:
            return list(self._models.keys())
    
    def get_registered_models(self) -> List[str]:
        """
        获取已注册的模型列表
        
        Returns:
            List[str]: 已注册的模型名称列表
        """
        with self._lock:
            return list(self._model_configs.keys())
    
    def get_model_stats(self, model_name: str) -> Optional[Dict[str, Any]]:
        """
        获取模型统计信息
        
        Args:
            model_name: 模型名称
            
        Returns:
            Dict: 模型统计信息或None
        """
        with self._lock:
            if model_name in self._models:
                return self._models[model_name].get_stats()
            return None
    
    def get_all_stats(self) -> Dict[str, Any]:
        """
        获取所有模型统计信息
        
        Returns:
            Dict: 所有模型的统计信息
        """
        with self._lock:
            stats = {
                "total_registered": len(self._model_configs),
                "total_loaded": len(self._models),
                "max_cache_size": self._max_cache_size,
                "models": {}
            }
            
            for model_name, model_instance in self._models.items():
                stats["models"][model_name] = model_instance.get_stats()
            
            return stats
    
    def _check_cache_size(self) -> None:
        """检查并清理缓存"""
        if len(self._models) >= self._max_cache_size:
            # 找到最久未使用的模型
            oldest_model = min(
                self._models.items(),
                key=lambda x: x[1].last_used
            )
            
            model_name = oldest_model[0]
            logger.info(f"缓存已满，卸载最久未使用的模型: {model_name}")
            self.unload_model(model_name)
    
    def cleanup_idle_models(self) -> int:
        """
        清理空闲模型
        
        Returns:
            int: 清理的模型数量
        """
        with self._lock:
            current_time = time.time()
            idle_models = []
            
            for model_name, model_instance in self._models.items():
                idle_time = current_time - model_instance.last_used
                if idle_time > self._max_idle_time:
                    idle_models.append(model_name)
            
            cleaned_count = 0
            for model_name in idle_models:
                if self.unload_model(model_name):
                    cleaned_count += 1
            
            if cleaned_count > 0:
                logger.info(f"清理了 {cleaned_count} 个空闲模型")
            
            return cleaned_count
    
    def preload_models(self, model_names: List[str]) -> Dict[str, bool]:
        """
        批量预加载模型
        
        Args:
            model_names: 模型名称列表
            
        Returns:
            Dict[str, bool]: 加载结果
        """
        results = {}
        for model_name in model_names:
            results[model_name] = self.preload_model(model_name)
        return results
    
    def unload_all_models(self) -> Dict[str, bool]:
        """
        卸载所有模型
        
        Returns:
            Dict[str, bool]: 卸载结果
        """
        with self._lock:
            model_names = list(self._models.keys())
            results = {}
            
            for model_name in model_names:
                results[model_name] = self.unload_model(model_name)
            
            return results
    
    def set_cache_config(self, max_cache_size: int = 5, max_idle_time: int = 3600) -> None:
        """
        设置缓存配置
        
        Args:
            max_cache_size: 最大缓存模型数
            max_idle_time: 最大空闲时间（秒）
        """
        with self._lock:
            self._max_cache_size = max_cache_size
            self._max_idle_time = max_idle_time
            logger.info(f"缓存配置已更新: max_cache_size={max_cache_size}, max_idle_time={max_idle_time}")
    
    def __del__(self):
        """析构函数，清理所有模型"""
        try:
            self.unload_all_models()
            logger.info("模型管理器已清理")
        except Exception as e:
            logger.error(f"模型管理器清理失败: {e}")


# 创建全局模型管理器实例
model_manager = ModelManager()
