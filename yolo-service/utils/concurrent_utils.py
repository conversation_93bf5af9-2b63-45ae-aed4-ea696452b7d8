"""
并发处理工具

提供线程安全的请求处理，避免多个设备或服务调用时的相互影响
"""

import time
import threading
import asyncio
import logging
from typing import Dict, Any, Optional, Callable, List
from concurrent.futures import ThreadPoolExecutor, Future, as_completed
from queue import Queue, Empty
from dataclasses import dataclass
from enum import Enum
import uuid

# 初始化日志
logger = logging.getLogger(__name__)


class RequestStatus(Enum):
    """请求状态枚举"""
    PENDING = "pending"
    PROCESSING = "processing"
    COMPLETED = "completed"
    FAILED = "failed"
    CANCELLED = "cancelled"


@dataclass
class RequestInfo:
    """请求信息"""
    request_id: str
    model_type: str
    status: RequestStatus
    created_at: float
    started_at: Optional[float] = None
    completed_at: Optional[float] = None
    result: Optional[Any] = None
    error: Optional[str] = None
    priority: int = 0  # 优先级，数字越小优先级越高


class RequestQueue:
    """线程安全的请求队列"""
    
    def __init__(self, max_size: int = 100):
        self.max_size = max_size
        self._queue = Queue(maxsize=max_size)
        self._requests: Dict[str, RequestInfo] = {}
        self._lock = threading.RLock()
    
    def add_request(self, request_info: RequestInfo) -> bool:
        """添加请求到队列"""
        with self._lock:
            try:
                if self._queue.full():
                    logger.warning("请求队列已满，拒绝新请求")
                    return False
                
                self._queue.put(request_info, block=False)
                self._requests[request_info.request_id] = request_info
                
                logger.debug(f"请求已添加到队列: {request_info.request_id}")
                return True
                
            except Exception as e:
                logger.error(f"添加请求失败: {e}")
                return False
    
    def get_request(self, timeout: float = 1.0) -> Optional[RequestInfo]:
        """从队列获取请求"""
        try:
            request_info = self._queue.get(timeout=timeout)
            with self._lock:
                if request_info.request_id in self._requests:
                    self._requests[request_info.request_id].status = RequestStatus.PROCESSING
                    self._requests[request_info.request_id].started_at = time.time()
            
            return request_info
            
        except Empty:
            return None
        except Exception as e:
            logger.error(f"获取请求失败: {e}")
            return None
    
    def update_request(self, request_id: str, status: RequestStatus, 
                      result: Any = None, error: str = None) -> None:
        """更新请求状态"""
        with self._lock:
            if request_id in self._requests:
                request_info = self._requests[request_id]
                request_info.status = status
                request_info.completed_at = time.time()
                
                if result is not None:
                    request_info.result = result
                if error is not None:
                    request_info.error = error
    
    def get_request_info(self, request_id: str) -> Optional[RequestInfo]:
        """获取请求信息"""
        with self._lock:
            return self._requests.get(request_id)
    
    def remove_request(self, request_id: str) -> None:
        """移除请求"""
        with self._lock:
            if request_id in self._requests:
                del self._requests[request_id]
    
    def get_queue_size(self) -> int:
        """获取队列大小"""
        return self._queue.qsize()
    
    def get_all_requests(self) -> List[RequestInfo]:
        """获取所有请求信息"""
        with self._lock:
            return list(self._requests.values())


class ConcurrentRequestProcessor:
    """并发请求处理器"""
    
    def __init__(self, max_workers: int = 4, max_queue_size: int = 100):
        self.max_workers = max_workers
        self.max_queue_size = max_queue_size
        
        # 为每个模型类型创建独立的队列和线程池
        self._queues: Dict[str, RequestQueue] = {}
        self._executors: Dict[str, ThreadPoolExecutor] = {}
        self._workers: Dict[str, List[threading.Thread]] = {}
        self._running = False
        self._lock = threading.RLock()
        
        logger.info(f"并发请求处理器初始化完成，最大工作线程数: {max_workers}")
    
    def start(self) -> None:
        """启动处理器"""
        with self._lock:
            if self._running:
                logger.warning("处理器已在运行")
                return
            
            self._running = True
            logger.info("并发请求处理器已启动")
    
    def stop(self) -> None:
        """停止处理器"""
        with self._lock:
            if not self._running:
                return
            
            self._running = False
            
            # 停止所有线程池
            for model_type, executor in self._executors.items():
                logger.info(f"停止线程池: {model_type}")
                executor.shutdown(wait=True)
            
            self._executors.clear()
            self._workers.clear()
            
            logger.info("并发请求处理器已停止")
    
    def _get_or_create_queue(self, model_type: str) -> RequestQueue:
        """获取或创建模型队列"""
        with self._lock:
            if model_type not in self._queues:
                self._queues[model_type] = RequestQueue(self.max_queue_size)
                logger.debug(f"为模型 {model_type} 创建了新队列")
            
            return self._queues[model_type]
    
    def _get_or_create_executor(self, model_type: str) -> ThreadPoolExecutor:
        """获取或创建线程池"""
        with self._lock:
            if model_type not in self._executors:
                self._executors[model_type] = ThreadPoolExecutor(
                    max_workers=self.max_workers,
                    thread_name_prefix=f"yolo-{model_type}"
                )
                logger.debug(f"为模型 {model_type} 创建了新线程池")
            
            return self._executors[model_type]
    
    def submit_request(self, model_type: str, detector_func: Callable, 
                      *args, **kwargs) -> str:
        """提交请求"""
        if not self._running:
            raise RuntimeError("处理器未运行")
        
        # 生成请求ID
        request_id = str(uuid.uuid4())
        
        # 创建请求信息
        request_info = RequestInfo(
            request_id=request_id,
            model_type=model_type,
            status=RequestStatus.PENDING,
            created_at=time.time()
        )
        
        # 获取队列和线程池
        queue = self._get_or_create_queue(model_type)
        executor = self._get_or_create_executor(model_type)
        
        # 添加到队列
        if not queue.add_request(request_info):
            raise RuntimeError("请求队列已满")
        
        # 提交到线程池
        future = executor.submit(self._process_request, queue, detector_func, *args, **kwargs)
        
        logger.info(f"请求已提交: {request_id}, 模型: {model_type}")
        return request_id
    
    def _process_request(self, queue: RequestQueue, detector_func: Callable, 
                        *args, **kwargs) -> None:
        """处理请求"""
        request_info = queue.get_request()
        if request_info is None:
            return
        
        request_id = request_info.request_id
        
        try:
            logger.debug(f"开始处理请求: {request_id}")
            
            # 执行检测
            result = detector_func(*args, **kwargs)
            
            # 更新请求状态
            queue.update_request(request_id, RequestStatus.COMPLETED, result=result)
            
            logger.debug(f"请求处理完成: {request_id}")
            
        except Exception as e:
            logger.error(f"请求处理失败: {request_id}, 错误: {e}")
            queue.update_request(request_id, RequestStatus.FAILED, error=str(e))
    
    def get_request_status(self, request_id: str, model_type: str) -> Optional[RequestInfo]:
        """获取请求状态"""
        if model_type in self._queues:
            return self._queues[model_type].get_request_info(request_id)
        return None
    
    def wait_for_result(self, request_id: str, model_type: str, 
                       timeout: float = 30.0) -> Any:
        """等待请求结果"""
        start_time = time.time()
        
        while time.time() - start_time < timeout:
            request_info = self.get_request_status(request_id, model_type)
            
            if request_info is None:
                raise ValueError(f"请求不存在: {request_id}")
            
            if request_info.status == RequestStatus.COMPLETED:
                return request_info.result
            elif request_info.status == RequestStatus.FAILED:
                raise RuntimeError(f"请求失败: {request_info.error}")
            elif request_info.status == RequestStatus.CANCELLED:
                raise RuntimeError("请求已取消")
            
            time.sleep(0.1)  # 短暂等待
        
        raise TimeoutError(f"请求超时: {request_id}")
    
    def get_statistics(self) -> Dict[str, Any]:
        """获取统计信息"""
        stats = {
            "total_queues": len(self._queues),
            "total_executors": len(self._executors),
            "running": self._running,
            "queues": {}
        }
        
        for model_type, queue in self._queues.items():
            requests = queue.get_all_requests()
            status_count = {}
            
            for request in requests:
                status = request.status.value
                status_count[status] = status_count.get(status, 0) + 1
            
            stats["queues"][model_type] = {
                "queue_size": queue.get_queue_size(),
                "total_requests": len(requests),
                "status_count": status_count
            }
        
        return stats
    
    def cleanup_old_requests(self, max_age_seconds: float = 3600) -> int:
        """清理旧请求"""
        current_time = time.time()
        cleaned_count = 0
        
        for model_type, queue in self._queues.items():
            requests = queue.get_all_requests()
            
            for request in requests:
                if (request.status in [RequestStatus.COMPLETED, RequestStatus.FAILED] and
                    current_time - request.created_at > max_age_seconds):
                    queue.remove_request(request.request_id)
                    cleaned_count += 1
        
        if cleaned_count > 0:
            logger.info(f"清理了 {cleaned_count} 个旧请求")
        
        return cleaned_count


# 全局并发处理器实例
_global_processor: Optional[ConcurrentRequestProcessor] = None
_processor_lock = threading.Lock()


def get_global_processor() -> ConcurrentRequestProcessor:
    """获取全局并发处理器"""
    global _global_processor
    
    with _processor_lock:
        if _global_processor is None:
            _global_processor = ConcurrentRequestProcessor()
            _global_processor.start()
        
        return _global_processor


def shutdown_global_processor():
    """关闭全局并发处理器"""
    global _global_processor
    
    with _processor_lock:
        if _global_processor is not None:
            _global_processor.stop()
            _global_processor = None
