"""
结果格式化工具

提供统一的结果格式化和转换功能
"""

import time
import logging
from typing import Dict, List, Any, Optional, Union
from pathlib import Path
import numpy as np

# 初始化日志
logger = logging.getLogger(__name__)


class ResultFormatter:
    """结果格式化器"""
    
    @staticmethod
    def format_detection_result(success: bool, devices: List[Dict], 
                              model_name: str, processing_time: float,
                              image_shape: Optional[tuple] = None,
                              saved_path: Optional[str] = None,
                              error: Optional[str] = None) -> Dict[str, Any]:
        """
        格式化检测结果
        
        Args:
            success: 检测是否成功
            devices: 检测到的设备列表
            model_name: 模型名称
            processing_time: 处理时间
            image_shape: 图像尺寸
            saved_path: 保存路径
            error: 错误信息
            
        Returns:
            Dict: 格式化的结果
        """
        result = {
            "success": success,
            "model_name": model_name,
            "processing_time": round(processing_time, 3),
            "timestamp": time.time(),
            "devices": devices or []
        }
        
        if image_shape:
            result["image_shape"] = list(image_shape)
        
        if saved_path:
            result["saved_path"] = saved_path
            result["download_url"] = f"/download/{Path(saved_path).name}"
        
        if error:
            result["error"] = error
            result["message"] = f"检测失败: {error}"
        else:
            result["message"] = "检测成功" if success else "检测失败"
        
        return result
    
    @staticmethod
    def format_segmentation_result(success: bool, objects: List[Dict],
                                 total_area: int, segmented_area: int,
                                 percentage: float, model_name: str,
                                 processing_time: float,
                                 image_shape: Optional[tuple] = None,
                                 class_stats: Optional[Dict] = None,
                                 saved_path: Optional[str] = None,
                                 error: Optional[str] = None) -> Dict[str, Any]:
        """
        格式化分割结果
        
        Args:
            success: 分割是否成功
            objects: 检测到的对象列表
            total_area: 总面积
            segmented_area: 分割面积
            percentage: 分割百分比
            model_name: 模型名称
            processing_time: 处理时间
            image_shape: 图像尺寸
            class_stats: 类别统计
            saved_path: 保存路径
            error: 错误信息
            
        Returns:
            Dict: 格式化的结果
        """
        result = {
            "success": success,
            "model_name": model_name,
            "processing_time": round(processing_time, 3),
            "timestamp": time.time(),
            "objects": objects or [],
            "objects_detail": objects or [],  # 兼容原有接口
            "total_area": total_area,
            "segmented_area": segmented_area,
            "percentage": round(percentage, 2),
            "num_objects": len(objects) if objects else 0
        }
        
        if image_shape:
            result["image_shape"] = list(image_shape)
        
        if class_stats:
            result["class_stats"] = class_stats
        
        if saved_path:
            result["saved_path"] = saved_path
            result["download_url"] = f"/download/{Path(saved_path).name}"
        
        if error:
            result["error"] = error
            result["message"] = f"分割失败: {error}"
        else:
            result["message"] = "分割成功" if success else "分割失败"
        
        return result
    
    @staticmethod
    def format_batch_result(results: List[Dict], processing_time: float,
                          total_images: int) -> Dict[str, Any]:
        """
        格式化批量处理结果
        
        Args:
            results: 处理结果列表
            processing_time: 总处理时间
            total_images: 总图像数
            
        Returns:
            Dict: 格式化的批量结果
        """
        successful_count = sum(1 for r in results if r.get("success", False))
        failed_count = total_images - successful_count
        
        # 计算统计信息
        statistics = {
            "total_processing_time": round(processing_time, 3),
            "average_processing_time": round(processing_time / total_images, 3) if total_images > 0 else 0,
            "success_rate": round(successful_count / total_images * 100, 2) if total_images > 0 else 0
        }
        
        return {
            "success": successful_count > 0,
            "message": f"批量处理完成，成功处理 {successful_count}/{total_images} 张图像",
            "total_images": total_images,
            "successful_images": successful_count,
            "failed_images": failed_count,
            "results": results,
            "statistics": statistics,
            "processing_time": round(processing_time, 3),
            "timestamp": time.time()
        }
    
    @staticmethod
    def format_error_result(error: str, model_name: str = "unknown",
                          processing_time: float = 0.0) -> Dict[str, Any]:
        """
        格式化错误结果
        
        Args:
            error: 错误信息
            model_name: 模型名称
            processing_time: 处理时间
            
        Returns:
            Dict: 格式化的错误结果
        """
        return {
            "success": False,
            "error": error,
            "message": f"处理失败: {error}",
            "model_name": model_name,
            "processing_time": round(processing_time, 3),
            "timestamp": time.time(),
            "devices": [],
            "objects": []
        }
    
    @staticmethod
    def format_device_info(device_id: Optional[int], status: str, 
                         confidence: float, bbox: List[float],
                         position: Optional[str] = None,
                         additional_info: Optional[Dict] = None) -> Dict[str, Any]:
        """
        格式化设备信息
        
        Args:
            device_id: 设备ID
            status: 设备状态
            confidence: 置信度
            bbox: 边界框
            position: 位置描述
            additional_info: 额外信息
            
        Returns:
            Dict: 格式化的设备信息
        """
        device_info = {
            "status": status,
            "confidence": round(confidence, 3),
            "bbox": [round(coord, 2) for coord in bbox]
        }
        
        if device_id is not None:
            device_info["device_id"] = device_id
            device_info["id"] = device_id  # 兼容性
        
        if position:
            device_info["position"] = position
        
        if additional_info:
            device_info.update(additional_info)
        
        return device_info
    
    @staticmethod
    def format_obb_device_info(device_id: Optional[int], status: str,
                             confidence: float, bbox: List[float],
                             obb_info: Dict[str, Any]) -> Dict[str, Any]:
        """
        格式化OBB设备信息
        
        Args:
            device_id: 设备ID
            status: 设备状态
            confidence: 置信度
            bbox: 水平边界框
            obb_info: OBB信息
            
        Returns:
            Dict: 格式化的OBB设备信息
        """
        device_info = ResultFormatter.format_device_info(
            device_id, status, confidence, bbox
        )
        device_info["obb"] = obb_info
        
        return device_info
    
    @staticmethod
    def format_segmentation_object(object_id: int, class_name: str,
                                 confidence: float, bbox: List[float],
                                 mask_area: int, area_percentage: float) -> Dict[str, Any]:
        """
        格式化分割对象信息
        
        Args:
            object_id: 对象ID
            class_name: 类别名称
            confidence: 置信度
            bbox: 边界框
            mask_area: 掩码面积
            area_percentage: 面积百分比
            
        Returns:
            Dict: 格式化的分割对象信息
        """
        return {
            "id": object_id,
            "class": class_name,
            "confidence": round(confidence, 3),
            "bbox": [round(coord, 2) for coord in bbox],
            "mask_area": mask_area,
            "area_percentage": round(area_percentage, 2)
        }
    
    @staticmethod
    def calculate_class_statistics(objects: List[Dict]) -> Dict[str, Any]:
        """
        计算类别统计信息
        
        Args:
            objects: 对象列表
            
        Returns:
            Dict: 类别统计信息
        """
        class_stats = {}
        
        for obj in objects:
            class_name = obj.get("class", "unknown")
            
            if class_name not in class_stats:
                class_stats[class_name] = {
                    "count": 0,
                    "total_area": 0,
                    "total_percentage": 0.0,
                    "avg_confidence": 0.0,
                    "confidences": []
                }
            
            stats = class_stats[class_name]
            stats["count"] += 1
            stats["total_area"] += obj.get("mask_area", 0)
            stats["total_percentage"] += obj.get("area_percentage", 0.0)
            stats["confidences"].append(obj.get("confidence", 0.0))
        
        # 计算平均置信度
        for class_name, stats in class_stats.items():
            if stats["confidences"]:
                stats["avg_confidence"] = round(
                    sum(stats["confidences"]) / len(stats["confidences"]), 3
                )
            del stats["confidences"]  # 移除临时数据
        
        return class_stats
    
    @staticmethod
    def format_health_response(status: str, model_loaded: bool,
                             service_version: str = "1.0.0",
                             loaded_models: Optional[List[str]] = None,
                             enabled_services: Optional[Dict[str, bool]] = None) -> Dict[str, Any]:
        """
        格式化健康检查响应
        
        Args:
            status: 服务状态
            model_loaded: 模型是否已加载
            service_version: 服务版本
            loaded_models: 已加载的模型列表
            enabled_services: 启用的服务
            
        Returns:
            Dict: 格式化的健康检查响应
        """
        return {
            "status": status,
            "model_loaded": model_loaded,
            "service_version": service_version,
            "loaded_models": loaded_models or [],
            "enabled_services": enabled_services or {},
            "timestamp": time.time()
        }
    
    @staticmethod
    def format_model_info(name: str, path: str, model_type: str,
                        loaded: bool, enabled: bool) -> Dict[str, Any]:
        """
        格式化模型信息
        
        Args:
            name: 模型名称
            path: 模型路径
            model_type: 模型类型
            loaded: 是否已加载
            enabled: 是否启用
            
        Returns:
            Dict: 格式化的模型信息
        """
        return {
            "name": name,
            "path": path,
            "type": model_type,
            "loaded": loaded,
            "enabled": enabled,
            "file_exists": Path(path).exists() if path else False
        }
