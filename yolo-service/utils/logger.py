"""
YOLO服务日志系统

集成现有的日志系统，确保所有YOLO服务的日志都存储到logs目录
"""

import os
import sys
import logging
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent.parent.parent
sys.path.append(str(project_root))

# 导入现有的日志系统
from server.utils.logger import setup_logging, DailyPathRotatingFileHandler, ColoredFormatter


class YoloServiceLogger:
    """YOLO服务日志管理器"""
    
    _initialized = False
    _loggers = {}
    
    @classmethod
    def setup_yolo_logging(cls, service_name: str = "yolo_service") -> logging.Logger:
        """
        设置YOLO服务日志
        
        Args:
            service_name: 服务名称
            
        Returns:
            logging.Logger: 配置好的日志记录器
        """
        if not cls._initialized:
            # 首先初始化主日志系统
            setup_logging()
            cls._initialized = True
        
        # 如果已经创建过该服务的日志记录器，直接返回
        if service_name in cls._loggers:
            return cls._loggers[service_name]
        
        # 创建服务专用的日志记录器
        logger = logging.getLogger(f"yolo_service.{service_name}")
        
        # 如果已经有处理器，说明已经配置过，直接返回
        if logger.handlers:
            cls._loggers[service_name] = logger
            return logger
        
        # 设置日志级别
        logger.setLevel(logging.INFO)
        
        # 创建YOLO服务专用的文件处理器
        try:
            from config_file import config
            log_path = Path(config.env['storage']['paths']['logs'])
            
            # 为YOLO服务创建专门的日志文件
            yolo_handler = DailyPathRotatingFileHandler(
                base_path=log_path,
                filename=f"{service_name}.log",
                max_bytes=52428800,  # 50MB
                backup_count=5
            )
            
            # 设置日志格式
            log_format = "%(asctime)s - %(levelname)s - [%(name)s] - %(filename)s:%(lineno)d - %(message)s"
            date_format = "%Y-%m-%d %H:%M:%S"
            
            file_formatter = logging.Formatter(log_format, date_format)
            yolo_handler.setFormatter(file_formatter)
            
            # 添加文件处理器
            logger.addHandler(yolo_handler)
            
            # 添加控制台处理器（带颜色）
            console_handler = logging.StreamHandler()
            colored_formatter = ColoredFormatter(log_format, date_format)
            console_handler.setFormatter(colored_formatter)
            logger.addHandler(console_handler)
            
            # 防止日志向上传播（避免重复记录）
            logger.propagate = False
            
            logger.info(f"YOLO服务日志系统初始化完成: {service_name}")
            
        except Exception as e:
            # 如果配置失败，使用基本的控制台日志
            console_handler = logging.StreamHandler()
            formatter = logging.Formatter(
                "%(asctime)s - %(levelname)s - [%(name)s] - %(message)s"
            )
            console_handler.setFormatter(formatter)
            logger.addHandler(console_handler)
            logger.warning(f"YOLO服务日志文件配置失败，使用控制台日志: {e}")
        
        # 缓存日志记录器
        cls._loggers[service_name] = logger
        
        return logger
    
    @classmethod
    def get_logger(cls, service_name: str = "yolo_service") -> logging.Logger:
        """
        获取日志记录器
        
        Args:
            service_name: 服务名称
            
        Returns:
            logging.Logger: 日志记录器
        """
        if service_name in cls._loggers:
            return cls._loggers[service_name]
        
        return cls.setup_yolo_logging(service_name)
    
    @classmethod
    def setup_component_logger(cls, component_name: str, 
                             parent_service: str = "yolo_service") -> logging.Logger:
        """
        为组件设置专用日志记录器
        
        Args:
            component_name: 组件名称
            parent_service: 父服务名称
            
        Returns:
            logging.Logger: 组件日志记录器
        """
        full_name = f"{parent_service}.{component_name}"
        
        if full_name in cls._loggers:
            return cls._loggers[full_name]
        
        # 创建组件日志记录器
        logger = logging.getLogger(f"yolo_service.{parent_service}.{component_name}")
        
        # 继承父服务的配置
        parent_logger = cls.get_logger(parent_service)
        logger.setLevel(parent_logger.level)
        
        # 组件日志记录器不需要单独的处理器，会继承父记录器的处理器
        logger.propagate = True
        
        cls._loggers[full_name] = logger
        
        return logger
    
    @classmethod
    def set_log_level(cls, level: str, service_name: str = "yolo_service"):
        """
        设置日志级别
        
        Args:
            level: 日志级别 (DEBUG, INFO, WARNING, ERROR, CRITICAL)
            service_name: 服务名称
        """
        logger = cls.get_logger(service_name)
        
        level_map = {
            'DEBUG': logging.DEBUG,
            'INFO': logging.INFO,
            'WARNING': logging.WARNING,
            'ERROR': logging.ERROR,
            'CRITICAL': logging.CRITICAL
        }
        
        if level.upper() in level_map:
            logger.setLevel(level_map[level.upper()])
            logger.info(f"日志级别已设置为: {level.upper()}")
        else:
            logger.warning(f"无效的日志级别: {level}")
    
    @classmethod
    def get_all_loggers(cls) -> dict:
        """
        获取所有日志记录器
        
        Returns:
            dict: 所有日志记录器的字典
        """
        return cls._loggers.copy()
    
    @classmethod
    def cleanup_loggers(cls):
        """清理所有日志记录器"""
        for logger_name, logger in cls._loggers.items():
            # 关闭所有处理器
            for handler in logger.handlers[:]:
                handler.close()
                logger.removeHandler(handler)
        
        cls._loggers.clear()
        cls._initialized = False


# 便捷函数
def get_yolo_logger(service_name: str = "yolo_service") -> logging.Logger:
    """
    获取YOLO服务日志记录器的便捷函数
    
    Args:
        service_name: 服务名称
        
    Returns:
        logging.Logger: 日志记录器
    """
    return YoloServiceLogger.get_logger(service_name)


def setup_detector_logger(detector_name: str) -> logging.Logger:
    """
    为检测器设置日志记录器
    
    Args:
        detector_name: 检测器名称
        
    Returns:
        logging.Logger: 检测器日志记录器
    """
    return YoloServiceLogger.setup_component_logger(detector_name, "detectors")


def setup_api_logger() -> logging.Logger:
    """
    为API设置日志记录器
    
    Returns:
        logging.Logger: API日志记录器
    """
    return YoloServiceLogger.setup_component_logger("api", "yolo_service")


def setup_core_logger(component_name: str) -> logging.Logger:
    """
    为核心组件设置日志记录器
    
    Args:
        component_name: 组件名称
        
    Returns:
        logging.Logger: 核心组件日志记录器
    """
    return YoloServiceLogger.setup_component_logger(component_name, "core")


# 创建默认的YOLO服务日志记录器
default_logger = YoloServiceLogger.setup_yolo_logging("main")


# 导出主要函数和类
__all__ = [
    "YoloServiceLogger",
    "get_yolo_logger",
    "setup_detector_logger",
    "setup_api_logger", 
    "setup_core_logger",
    "default_logger"
]
