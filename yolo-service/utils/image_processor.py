"""
图像处理工具

提供图像处理、验证、转换等功能
"""

import os
import io
import base64
import logging
from typing import Union, Tuple, Optional, List
from pathlib import Path
import numpy as np
import cv2
from PIL import Image

# 初始化日志
logger = logging.getLogger(__name__)


class ImageProcessor:
    """图像处理器"""
    
    # 支持的图像格式
    SUPPORTED_FORMATS = {'.jpg', '.jpeg', '.png', '.bmp', '.tiff', '.webp'}
    
    @staticmethod
    def validate_image_format(file_path: str) -> bool:
        """
        验证图像格式
        
        Args:
            file_path: 图像文件路径
            
        Returns:
            bool: 是否为支持的格式
        """
        file_ext = Path(file_path).suffix.lower()
        return file_ext in ImageProcessor.SUPPORTED_FORMATS
    
    @staticmethod
    def load_image(image_input: Union[str, np.ndarray, bytes]) -> <PERSON><PERSON>[np.ndarray, Optional[str]]:
        """
        加载图像
        
        Args:
            image_input: 图像输入（文件路径、numpy数组或字节数据）
            
        Returns:
            Tuple[np.ndarray, Optional[str]]: (图像数组, 原始路径)
            
        Raises:
            FileNotFoundError: 文件不存在
            ValueError: 无法读取图像
            TypeError: 不支持的输入类型
        """
        original_path = None
        
        if isinstance(image_input, str):
            # 处理文件路径
            if not os.path.exists(image_input):
                raise FileNotFoundError(f"图像文件不存在: {image_input}")
            
            if not ImageProcessor.validate_image_format(image_input):
                raise ValueError(f"不支持的图像格式: {image_input}")
            
            original_path = image_input
            image = cv2.imread(image_input)
            
            if image is None:
                raise ValueError(f"无法读取图像: {image_input}")
            
        elif isinstance(image_input, np.ndarray):
            # 处理numpy数组
            if image_input.size == 0:
                raise ValueError("输入图像为空")
            
            image = image_input.copy()
            
        elif isinstance(image_input, bytes):
            # 处理字节数据
            try:
                image_array = np.frombuffer(image_input, dtype=np.uint8)
                image = cv2.imdecode(image_array, cv2.IMREAD_COLOR)
                
                if image is None:
                    raise ValueError("无法解码图像数据")
                    
            except Exception as e:
                raise ValueError(f"解码图像数据失败: {e}")
        
        else:
            raise TypeError(f"不支持的输入类型: {type(image_input)}")
        
        return image, original_path
    
    @staticmethod
    def decode_base64_image(base64_data: str) -> np.ndarray:
        """
        解码Base64图像数据
        
        Args:
            base64_data: Base64编码的图像数据
            
        Returns:
            np.ndarray: 图像数组
            
        Raises:
            ValueError: 解码失败
        """
        try:
            # 移除可能的数据URL前缀
            if base64_data.startswith('data:image'):
                base64_data = base64_data.split(',')[1]
            
            # 解码Base64数据
            image_bytes = base64.b64decode(base64_data)
            
            # 转换为numpy数组
            image_array = np.frombuffer(image_bytes, dtype=np.uint8)
            image = cv2.imdecode(image_array, cv2.IMREAD_COLOR)
            
            if image is None:
                raise ValueError("无法解码图像数据")
            
            return image
            
        except Exception as e:
            raise ValueError(f"Base64图像解码失败: {e}")
    
    @staticmethod
    def encode_image_to_base64(image: np.ndarray, format: str = '.jpg') -> str:
        """
        将图像编码为Base64
        
        Args:
            image: 图像数组
            format: 图像格式
            
        Returns:
            str: Base64编码的图像数据
        """
        try:
            # 编码图像
            success, encoded_image = cv2.imencode(format, image)
            
            if not success:
                raise ValueError("图像编码失败")
            
            # 转换为Base64
            image_bytes = encoded_image.tobytes()
            base64_data = base64.b64encode(image_bytes).decode('utf-8')
            
            return base64_data
            
        except Exception as e:
            raise ValueError(f"图像Base64编码失败: {e}")
    
    @staticmethod
    def resize_image(image: np.ndarray, target_size: Tuple[int, int], 
                    keep_aspect_ratio: bool = True) -> np.ndarray:
        """
        调整图像大小
        
        Args:
            image: 输入图像
            target_size: 目标尺寸 (width, height)
            keep_aspect_ratio: 是否保持宽高比
            
        Returns:
            np.ndarray: 调整后的图像
        """
        height, width = image.shape[:2]
        target_width, target_height = target_size
        
        if keep_aspect_ratio:
            # 计算缩放比例
            scale = min(target_width / width, target_height / height)
            new_width = int(width * scale)
            new_height = int(height * scale)
            
            # 调整大小
            resized = cv2.resize(image, (new_width, new_height), interpolation=cv2.INTER_AREA)
            
            # 创建目标尺寸的画布
            canvas = np.zeros((target_height, target_width, image.shape[2]), dtype=image.dtype)
            
            # 计算居中位置
            y_offset = (target_height - new_height) // 2
            x_offset = (target_width - new_width) // 2
            
            # 将调整后的图像放置在画布中心
            canvas[y_offset:y_offset + new_height, x_offset:x_offset + new_width] = resized
            
            return canvas
        else:
            # 直接调整到目标尺寸
            return cv2.resize(image, target_size, interpolation=cv2.INTER_AREA)
    
    @staticmethod
    def normalize_image(image: np.ndarray) -> np.ndarray:
        """
        标准化图像
        
        Args:
            image: 输入图像
            
        Returns:
            np.ndarray: 标准化后的图像
        """
        # 转换为float32并归一化到[0, 1]
        normalized = image.astype(np.float32) / 255.0
        return normalized
    
    @staticmethod
    def denormalize_image(image: np.ndarray) -> np.ndarray:
        """
        反标准化图像
        
        Args:
            image: 标准化的图像
            
        Returns:
            np.ndarray: 反标准化后的图像
        """
        # 从[0, 1]转换回[0, 255]
        denormalized = (image * 255.0).astype(np.uint8)
        return denormalized
    
    @staticmethod
    def convert_color_space(image: np.ndarray, conversion: int) -> np.ndarray:
        """
        转换颜色空间
        
        Args:
            image: 输入图像
            conversion: 转换类型（cv2.COLOR_*）
            
        Returns:
            np.ndarray: 转换后的图像
        """
        return cv2.cvtColor(image, conversion)
    
    @staticmethod
    def save_image(image: np.ndarray, save_path: str, quality: int = 95) -> bool:
        """
        保存图像
        
        Args:
            image: 图像数组
            save_path: 保存路径
            quality: 图像质量（对JPEG有效）
            
        Returns:
            bool: 保存是否成功
        """
        try:
            # 确保保存目录存在
            save_dir = Path(save_path).parent
            save_dir.mkdir(parents=True, exist_ok=True)
            
            # 根据文件扩展名设置保存参数
            file_ext = Path(save_path).suffix.lower()
            
            if file_ext in ['.jpg', '.jpeg']:
                # JPEG格式，设置质量
                success = cv2.imwrite(save_path, image, [cv2.IMWRITE_JPEG_QUALITY, quality])
            elif file_ext == '.png':
                # PNG格式，设置压缩级别
                success = cv2.imwrite(save_path, image, [cv2.IMWRITE_PNG_COMPRESSION, 9])
            else:
                # 其他格式，使用默认参数
                success = cv2.imwrite(save_path, image)
            
            if success:
                logger.debug(f"图像已保存: {save_path}")
            else:
                logger.error(f"图像保存失败: {save_path}")
            
            return success
            
        except Exception as e:
            logger.error(f"保存图像时出错: {e}")
            return False
    
    @staticmethod
    def get_image_info(image: np.ndarray) -> dict:
        """
        获取图像信息
        
        Args:
            image: 图像数组
            
        Returns:
            dict: 图像信息
        """
        height, width = image.shape[:2]
        channels = image.shape[2] if len(image.shape) > 2 else 1
        
        return {
            "width": width,
            "height": height,
            "channels": channels,
            "shape": image.shape,
            "dtype": str(image.dtype),
            "size": image.size,
            "aspect_ratio": width / height
        }
    
    @staticmethod
    def validate_image_size(image: np.ndarray, min_size: Tuple[int, int] = (32, 32),
                           max_size: Tuple[int, int] = (4096, 4096)) -> bool:
        """
        验证图像尺寸
        
        Args:
            image: 图像数组
            min_size: 最小尺寸 (width, height)
            max_size: 最大尺寸 (width, height)
            
        Returns:
            bool: 尺寸是否有效
        """
        height, width = image.shape[:2]
        min_width, min_height = min_size
        max_width, max_height = max_size
        
        return (min_width <= width <= max_width and 
                min_height <= height <= max_height)
