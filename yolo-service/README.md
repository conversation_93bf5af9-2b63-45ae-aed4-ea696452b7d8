# YOLO推理服务

基于YOLO的多模型推理Web API服务，提供统一的接口和管理功能。

## 🚀 特性

- **多模型支持**: 支持检测、分割、OBB等多种YOLO模型
- **统一API**: 提供RESTful API接口，支持文件上传和Base64编码
- **线程安全**: 支持并发请求处理，避免模型冲突
- **配置化管理**: 通过配置文件管理服务启用状态和参数
- **模型预加载**: 支持服务启动时预加载模型，提高响应速度
- **结果缓存**: 智能的结果保存和下载功能
- **健康监控**: 提供服务健康检查和状态监控
- **日志系统**: 集成统一的日志系统，便于调试和监控

## 📁 项目结构

```
yolo-service/
├── app.py                  # 服务入口
├── start_service.py        # 启动脚本
├── config/                 # 配置管理
│   ├── service_config.py   # 服务配置
│   └── model_config.py     # 模型配置
├── core/                   # 核心组件
│   ├── base_detector.py    # 检测器基类
│   ├── model_manager.py    # 模型管理器
│   └── service_manager.py  # 服务管理器
├── detectors/              # 检测器实现
│   ├── dipper_detector.py  # 耙斗检测器
│   ├── filter_detector.py  # 滤池检测器
│   ├── shaft_detector.py   # 耙斗井检测器
│   └── segmentation_detector.py # 分割检测器
├── api/                    # API接口
│   ├── routes.py           # 路由定义
│   ├── schemas.py          # 数据模型
│   └── middleware.py       # 中间件
├── utils/                  # 工具模块
│   ├── image_processor.py  # 图像处理
│   ├── result_formatter.py # 结果格式化
│   ├── concurrent_utils.py # 并发工具
│   └── logger.py           # 日志系统
├── services/               # 业务服务
├── tests/                  # 测试用例
├── uploads/                # 上传目录
└── outputs/                # 输出目录
```

## 🛠️ 安装和配置

### 1. 依赖安装

```bash
pip install fastapi uvicorn ultralytics opencv-python numpy pydantic
```

### 2. 配置文件

在项目根目录的 `configs/env.yaml` 中添加YOLO服务配置：

```yaml
yolo_service:
  server:
    host: "0.0.0.0"
    port: 8001
  
  models:
    paths:
      dipper: "llms/models/yolo/best-dipper.pt"
      filter: "llms/models/yolo/best-filter.pt"
      shaft: "llms/models/yolo/best-dipper-shaft.pt"
      segmentation: "llms/models/yolo/best.pt"
  
  enabled_services:
    dipper_detection: true
    filter_detection: true
    shaft_detection: true
    segmentation: false
```

### 3. 模型文件

确保模型文件存在于指定路径：
- `llms/models/yolo/best-dipper.pt` - 耙斗检测模型
- `llms/models/yolo/best-filter.pt` - 滤池检测模型
- `llms/models/yolo/best-dipper-shaft.pt` - 耙斗井OBB检测模型
- `llms/models/yolo/best.pt` - 分割模型

## 🚀 启动服务

### 方式一：使用启动脚本（推荐）

```bash
cd yolo-service
python start_service.py
```

启动脚本会自动：
- 检查依赖项
- 验证模型文件
- 创建必要目录
- 运行基本测试
- 启动服务

### 方式二：直接启动

```bash
cd yolo-service
python app.py
```

### 方式三：使用uvicorn

```bash
cd yolo-service
uvicorn app:create_app --host 0.0.0.0 --port 8001 --reload
```

## 📚 API文档

服务启动后，可以通过以下地址访问API文档：

- **Swagger UI**: http://localhost:8001/docs
- **ReDoc**: http://localhost:8001/redoc
- **API根路径**: http://localhost:8001/api/v1

## 🔧 API使用示例

### 1. 健康检查

```bash
curl http://localhost:8001/api/v1/health
```

### 2. 上传图像检测

```bash
curl -X POST "http://localhost:8001/api/v1/detect/upload" \
  -H "Content-Type: multipart/form-data" \
  -F "file=@test_image.jpg" \
  -F "model_type=dipper" \
  -F "save_result=true"
```

### 3. Base64图像检测

```bash
curl -X POST "http://localhost:8001/api/v1/detect/base64" \
  -H "Content-Type: application/json" \
  -d '{
    "model_type": "filter",
    "image_data": "base64_encoded_image_data",
    "save_result": true,
    "conf": 0.5
  }'
```

### 4. 获取模型信息

```bash
curl http://localhost:8001/api/v1/models
```

### 5. 下载结果

```bash
curl -O http://localhost:8001/api/v1/download/result_filename.jpg
```

## 🔧 配置选项

### 服务配置

- `host`: 服务器主机地址
- `port`: 服务器端口
- `max_workers`: 最大工作线程数
- `max_concurrent_requests`: 最大并发请求数

### 模型配置

- `preload`: 是否预加载模型
- `conf`: 置信度阈值
- `iou`: IoU阈值
- `max_det`: 最大检测数量
- `imgsz`: 推理图像大小

### 文件处理配置

- `upload_dir`: 上传目录
- `output_dir`: 输出目录
- `max_file_size`: 最大文件大小
- `allowed_extensions`: 允许的文件扩展名

## 🧪 测试

### 运行基本测试

```bash
cd yolo-service
python tests/test_basic_functionality.py
```

### 使用启动脚本测试

```bash
python start_service.py --check-only
```

## 🔍 监控和调试

### 查看日志

日志文件位于 `logs/` 目录下：
- `yolo_service.log` - 主服务日志
- `yolo_service.detectors.log` - 检测器日志
- `yolo_service.api.log` - API日志

### 服务状态

```bash
curl http://localhost:8001/api/v1/services
```

### 配置信息

```bash
curl http://localhost:8001/api/v1/config
```

## 🚨 故障排除

### 常见问题

1. **模型文件不存在**
   - 检查模型文件路径是否正确
   - 确保模型文件已下载到指定位置

2. **端口被占用**
   - 修改配置文件中的端口号
   - 或使用 `--port` 参数指定其他端口

3. **内存不足**
   - 减少预加载的模型数量
   - 调整 `max_workers` 参数

4. **依赖包缺失**
   - 运行 `pip install -r requirements.txt`
   - 或使用启动脚本自动检查

### 调试模式

```bash
python app.py --log-level debug --reload
```

## 📝 开发指南

### 添加新的检测器

1. 在 `detectors/` 目录下创建新的检测器类
2. 继承 `BaseDetector` 基类
3. 实现 `_parse_results` 方法
4. 在 `app.py` 中注册新服务

### 扩展API接口

1. 在 `api/schemas.py` 中定义数据模型
2. 在 `api/routes.py` 中添加新的路由
3. 更新API文档

## 📄 许可证

本项目采用 MIT 许可证。

## 🤝 贡献

欢迎提交Issue和Pull Request来改进这个项目。
