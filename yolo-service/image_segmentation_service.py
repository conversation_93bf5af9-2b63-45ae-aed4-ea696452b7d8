import os
import logging
import threading
from pathlib import Path
from typing import Union, List, Dict, Any, Optional
from concurrent.futures import ThreadPoolExecutor, as_completed
import numpy as np
import cv2
from ultralytics import YOLO
from ultralytics.utils import ThreadingLocked

# 配置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

class ImageSegmentationService:
    """
    图像分割服务类
    支持单张图片和批量图片处理，线程安全
    """
    
    def __init__(self, model_path: str, max_workers: int = 4):
        """
        初始化图像分割服务
        
        Args:
            model_path (str): YOLO模型文件路径
            max_workers (int): 最大线程数，默认为4
        """
        self.model_path = model_path
        self.max_workers = max_workers
        self._lock = threading.Lock()
        
        # 验证模型文件是否存在
        if not os.path.exists(model_path):
            raise FileNotFoundError(f"模型文件不存在: {model_path}")
        
        logger.info(f"图像分割服务初始化完成，模型路径: {model_path}")

    def _createModel(self) -> YOLO:
        """
        创建YOLO模型实例（线程安全）
        每个线程都会创建自己的模型实例
        
        Returns:
            YOLO: YOLO模型实例
        """
        try:
            model = YOLO(self.model_path)
            logger.debug(f"线程 {threading.current_thread().name} 创建模型实例成功")
            return model
        except Exception as e:
            logger.error(f"创建模型实例失败: {e}")
            raise

    def _processNumpyImage(self, image_array: np.ndarray, save_path: str, 
                          save_annotated: bool = True, conf: float = 0.25, 
                          iou: float = 0.7, imgsz: int = 640) -> Dict[str, Any]:
        """
        处理numpy格式的图像
        
        Args:
            image_array (np.ndarray): 输入图像数组
            save_path (str): 保存路径
            save_annotated (bool): 是否保存标注图像
            conf (float): 置信度阈值
            iou (float): IoU阈值
            imgsz (int): 推理图像大小
            
        Returns:
            Dict[str, Any]: 处理结果
        """
        try:
            # 创建线程本地的模型实例
            model = self._createModel()
            
            # 验证图像数组格式
            if not isinstance(image_array, np.ndarray):
                raise ValueError("输入必须是numpy数组")
            
            if len(image_array.shape) != 3 or image_array.shape[2] != 3:
                raise ValueError("图像数组必须是3通道的彩色图像 (H, W, 3)")
            
            # 执行预测
            results = model.predict(
                source=image_array,
                conf=conf,
                iou=iou,
                imgsz=imgsz,
                save=False,
                verbose=False
            )
            
            if not results:
                logger.warning("未获得预测结果")
                return self._createEmptyResult(save_path)
            
            result = results[0]  # 单张图片只有一个结果
            
            # 计算面积信息
            area_info = self._calculateAreaInfo(result)
            
            # 保存图像
            saved_path = self._saveImage(result, image_array, save_path, save_annotated, area_info)
            
            # 构建返回结果
            return {
                "success": True,
                "saved_path": saved_path,
                "total_area": area_info["total_area"],
                "segmented_area": area_info["segmented_area"],
                "percentage": area_info["percentage"],
                "num_objects": area_info["num_objects"],
                "objects_detail": area_info["objects_detail"],
                "image_shape": image_array.shape[:2]  # (height, width)
            }
            
        except Exception as e:
            logger.error(f"处理numpy图像时出错: {e}")
            return {
                "success": False,
                "error": str(e),
                "saved_path": None,
                "total_area": 0,
                "segmented_area": 0,
                "percentage": 0.0,
                "num_objects": 0,
                "objects_detail": [],
                "image_shape": None
            }

    def _processImagePath(self, image_path: str, save_path: str, 
                         save_annotated: bool = True, conf: float = 0.25, 
                         iou: float = 0.7, imgsz: int = 640) -> Dict[str, Any]:
        """
        处理图像路径
        
        Args:
            image_path (str): 输入图像路径
            save_path (str): 保存路径
            save_annotated (bool): 是否保存标注图像
            conf (float): 置信度阈值
            iou (float): IoU阈值
            imgsz (int): 推理图像大小
            
        Returns:
            Dict[str, Any]: 处理结果
        """
        try:
            # 验证输入文件是否存在
            if not os.path.exists(image_path):
                raise FileNotFoundError(f"输入图像文件不存在: {image_path}")
            
            # 创建线程本地的模型实例
            model = self._createModel()
            
            # 读取原始图像
            original_image = cv2.imread(image_path)
            if original_image is None:
                raise ValueError(f"无法读取图像文件: {image_path}")
            
            # 执行预测
            results = model.predict(
                source=image_path,
                conf=conf,
                iou=iou,
                imgsz=imgsz,
                save=False,
                verbose=False
            )
            
            if not results:
                logger.warning(f"图像 {image_path} 未获得预测结果")
                return self._createEmptyResult(save_path)
            
            result = results[0]  # 单张图片只有一个结果
            
            # 计算面积信息
            area_info = self._calculateAreaInfo(result)
            
            # 保存图像
            saved_path = self._saveImage(result, original_image, save_path, save_annotated, area_info)
            
            # 构建返回结果
            return {
                "success": True,
                "input_path": image_path,
                "saved_path": saved_path,
                "total_area": area_info["total_area"],
                "segmented_area": area_info["segmented_area"],
                "percentage": area_info["percentage"],
                "num_objects": area_info["num_objects"],
                "objects_detail": area_info["objects_detail"],
                "image_shape": original_image.shape[:2]  # (height, width)
            }
            
        except Exception as e:
            logger.error(f"处理图像路径 {image_path} 时出错: {e}")
            return {
                "success": False,
                "input_path": image_path,
                "error": str(e),
                "saved_path": None,
                "total_area": 0,
                "segmented_area": 0,
                "percentage": 0.0,
                "num_objects": 0,
                "objects_detail": [],
                "image_shape": None
            }

    def _calculateAreaInfo(self, result) -> Dict[str, Any]:
        """
        计算分割面积信息
        
        Args:
            result: YOLO预测结果
            
        Returns:
            Dict[str, Any]: 面积信息
        """
        try:
            # 获取原始图像尺寸
            orig_height, orig_width = result.orig_shape
            total_area = orig_height * orig_width
            
            area_info = {
                "total_area": total_area,
                "segmented_area": 0,
                "percentage": 0.0,
                "num_objects": 0,
                "objects_detail": []
            }
            
            if result.masks is not None:
                masks_data = result.masks.data.cpu().numpy()
                num_objects = len(masks_data)
                area_info["num_objects"] = num_objects
                
                total_segmented_area = 0
                
                # 获取置信度和类别信息
                confidences = result.boxes.conf.cpu().numpy() if result.boxes is not None else None
                classes = result.boxes.cls.cpu().numpy() if result.boxes is not None else None
                
                for i, mask in enumerate(masks_data):
                    # 调整掩码到原始图像尺寸
                    if mask.shape != (orig_height, orig_width):
                        mask_resized = cv2.resize(mask.astype(np.uint8),
                                                (orig_width, orig_height),
                                                interpolation=cv2.INTER_NEAREST)
                        mask = mask_resized.astype(bool)
                    else:
                        mask = mask.astype(bool)
                    
                    # 计算当前对象的面积
                    object_area = np.sum(mask)
                    total_segmented_area += object_area
                    object_percentage = (object_area / total_area) * 100
                    
                    # 构建对象详细信息
                    object_detail = {
                        "object_id": i + 1,
                        "area": int(object_area),
                        "percentage": round(object_percentage, 2)
                    }
                    
                    if confidences is not None and i < len(confidences):
                        object_detail["confidence"] = round(float(confidences[i]), 3)
                    
                    if classes is not None and i < len(classes):
                        object_detail["class_id"] = int(classes[i])
                    
                    area_info["objects_detail"].append(object_detail)
                
                area_info["segmented_area"] = int(total_segmented_area)
                area_info["percentage"] = round((total_segmented_area / total_area) * 100, 2)
            
            return area_info
            
        except Exception as e:
            logger.error(f"计算面积信息时出错: {e}")
            return {
                "total_area": 0,
                "segmented_area": 0,
                "percentage": 0.0,
                "num_objects": 0,
                "objects_detail": []
            }

    def _saveImage(self, result, original_image: np.ndarray, save_path: str,
                   save_annotated: bool, area_info: Dict[str, Any]) -> str:
        """
        保存图像

        Args:
            result: YOLO预测结果
            original_image (np.ndarray): 原始图像
            save_path (str): 保存路径
            save_annotated (bool): 是否保存标注图像
            area_info (Dict[str, Any]): 面积信息

        Returns:
            str: 实际保存的文件路径
        """
        try:
            # 确保保存目录存在
            save_dir = Path(save_path).parent
            save_dir.mkdir(parents=True, exist_ok=True)

            # 构建文件名（包含面积信息）
            base_path = Path(save_path)
            stem = base_path.stem
            suffix = base_path.suffix or '.jpg'

            # 添加面积信息到文件名
            percentage = area_info.get("percentage", 0.0)
            final_filename = f"{stem}_area_{percentage:.2f}%{suffix}"
            final_path = save_dir / final_filename

            if save_annotated and result.masks is not None:
                # 保存标注图像
                annotated_image = result.plot(
                    conf=True,           # 显示置信度
                    labels=True,         # 显示标签
                    boxes=True,          # 显示边界框
                    masks=True,          # 显示分割掩码
                    line_width=2,        # 线条宽度
                    font_size=None,      # 字体大小（自动）
                    pil=False           # 返回numpy数组
                )

                # 转换颜色格式（RGB -> BGR）
                if annotated_image is not None:
                    annotated_image_bgr = cv2.cvtColor(annotated_image, cv2.COLOR_RGB2BGR)
                    success = cv2.imwrite(str(final_path), annotated_image_bgr)
                else:
                    # 如果标注失败，保存原图
                    success = cv2.imwrite(str(final_path), original_image)
            else:
                # 保存原始图像
                success = cv2.imwrite(str(final_path), original_image)

            if success:
                logger.info(f"图像已保存: {final_path}")
                return str(final_path)
            else:
                logger.error(f"保存图像失败: {final_path}")
                return None

        except Exception as e:
            logger.error(f"保存图像时出错: {e}")
            return None

    def _createEmptyResult(self, save_path: str) -> Dict[str, Any]:
        """
        创建空结果（当没有检测到分割对象时）

        Args:
            save_path (str): 保存路径

        Returns:
            Dict[str, Any]: 空结果
        """
        return {
            "success": True,
            "saved_path": save_path,
            "total_area": 0,
            "segmented_area": 0,
            "percentage": 0.0,
            "num_objects": 0,
            "objects_detail": [],
            "image_shape": None
        }

    def processImage(self, image_input: Union[str, np.ndarray], save_path: str,
                    save_annotated: bool = True, conf: float = 0.25,
                    iou: float = 0.7, imgsz: int = 640) -> Dict[str, Any]:
        """
        处理单张图像（主要接口函数）

        Args:
            image_input (Union[str, np.ndarray]): 输入图像（路径或numpy数组）
            save_path (str): 保存路径
            save_annotated (bool): 是否保存标注图像（True=标注图，False=原图）
            conf (float): 置信度阈值 (0.0-1.0)
            iou (float): IoU阈值 (0.0-1.0)
            imgsz (int): 推理图像大小

        Returns:
            Dict[str, Any]: 处理结果，包含：
                - success (bool): 是否成功
                - saved_path (str): 保存的文件路径
                - total_area (int): 图像总面积（像素）
                - segmented_area (int): 分割面积（像素）
                - percentage (float): 分割面积占比（%）
                - num_objects (int): 检测到的对象数量
                - objects_detail (List[Dict]): 每个对象的详细信息
                - image_shape (Tuple): 图像尺寸 (height, width)
        """
        try:
            logger.info(f"开始处理图像，保存路径: {save_path}")

            if isinstance(image_input, str):
                # 处理图像路径
                return self._processImagePath(image_input, save_path, save_annotated, conf, iou, imgsz)
            elif isinstance(image_input, np.ndarray):
                # 处理numpy数组
                return self._processNumpyImage(image_input, save_path, save_annotated, conf, iou, imgsz)
            else:
                raise ValueError("image_input必须是字符串路径或numpy数组")

        except Exception as e:
            logger.error(f"处理图像时出错: {e}")
            return {
                "success": False,
                "error": str(e),
                "saved_path": None,
                "total_area": 0,
                "segmented_area": 0,
                "percentage": 0.0,
                "num_objects": 0,
                "objects_detail": [],
                "image_shape": None
            }

    def processBatchImages(self, image_inputs: List[Union[str, np.ndarray]],
                          save_dir: str, save_annotated: bool = True,
                          conf: float = 0.25, iou: float = 0.7, imgsz: int = 640) -> List[Dict[str, Any]]:
        """
        批量处理多张图像（支持多线程）

        Args:
            image_inputs (List[Union[str, np.ndarray]]): 输入图像列表（路径或numpy数组）
            save_dir (str): 保存目录
            save_annotated (bool): 是否保存标注图像
            conf (float): 置信度阈值
            iou (float): IoU阈值
            imgsz (int): 推理图像大小

        Returns:
            List[Dict[str, Any]]: 处理结果列表
        """
        try:
            logger.info(f"开始批量处理 {len(image_inputs)} 张图像")

            # 确保保存目录存在
            save_path = Path(save_dir)
            save_path.mkdir(parents=True, exist_ok=True)

            results = []

            # 使用线程池进行并行处理
            with ThreadPoolExecutor(max_workers=self.max_workers) as executor:
                # 提交所有任务
                future_to_input = {}

                for i, image_input in enumerate(image_inputs):
                    # 为每个图像生成保存路径
                    if isinstance(image_input, str):
                        # 从路径提取文件名
                        filename = Path(image_input).stem
                        extension = Path(image_input).suffix or '.jpg'
                    else:
                        # numpy数组使用索引命名
                        filename = f"image_{i:04d}"
                        extension = '.jpg'

                    individual_save_path = save_path / f"{filename}{extension}"

                    # 提交任务到线程池
                    future = executor.submit(
                        self.processImage,
                        image_input,
                        str(individual_save_path),
                        save_annotated,
                        conf,
                        iou,
                        imgsz
                    )
                    future_to_input[future] = {
                        "index": i,
                        "input": image_input,
                        "save_path": str(individual_save_path)
                    }

                # 收集结果
                for future in as_completed(future_to_input):
                    input_info = future_to_input[future]
                    try:
                        result = future.result()
                        result["batch_index"] = input_info["index"]
                        if isinstance(input_info["input"], str):
                            result["input_path"] = input_info["input"]
                        results.append(result)

                        if result["success"]:
                            logger.info(f"批量处理 {input_info['index']+1}/{len(image_inputs)} 完成")
                        else:
                            logger.warning(f"批量处理 {input_info['index']+1}/{len(image_inputs)} 失败: {result.get('error', '未知错误')}")

                    except Exception as e:
                        logger.error(f"批量处理 {input_info['index']+1}/{len(image_inputs)} 时出现异常: {e}")
                        results.append({
                            "success": False,
                            "batch_index": input_info["index"],
                            "error": str(e),
                            "saved_path": None,
                            "total_area": 0,
                            "segmented_area": 0,
                            "percentage": 0.0,
                            "num_objects": 0,
                            "objects_detail": [],
                            "image_shape": None
                        })

            # 按索引排序结果
            results.sort(key=lambda x: x.get("batch_index", 0))

            # 统计处理结果
            successful_count = sum(1 for r in results if r["success"])
            logger.info(f"批量处理完成: {successful_count}/{len(image_inputs)} 张图像处理成功")

            return results

        except Exception as e:
            logger.error(f"批量处理时出错: {e}")
            return [{
                "success": False,
                "error": str(e),
                "saved_path": None,
                "total_area": 0,
                "segmented_area": 0,
                "percentage": 0.0,
                "num_objects": 0,
                "objects_detail": [],
                "image_shape": None
            }]

    def getBatchStatistics(self, batch_results: List[Dict[str, Any]]) -> Dict[str, Any]:
        """
        获取批量处理的统计信息

        Args:
            batch_results (List[Dict[str, Any]]): 批量处理结果

        Returns:
            Dict[str, Any]: 统计信息
        """
        try:
            successful_results = [r for r in batch_results if r.get("success", False)]

            if not successful_results:
                return {
                    "total_images": len(batch_results),
                    "successful_images": 0,
                    "failed_images": len(batch_results),
                    "total_objects": 0,
                    "average_percentage": 0.0,
                    "max_percentage": 0.0,
                    "min_percentage": 0.0,
                    "total_segmented_area": 0,
                    "total_image_area": 0
                }

            total_objects = sum(r.get("num_objects", 0) for r in successful_results)
            percentages = [r.get("percentage", 0.0) for r in successful_results]
            total_segmented_area = sum(r.get("segmented_area", 0) for r in successful_results)
            total_image_area = sum(r.get("total_area", 0) for r in successful_results)

            return {
                "total_images": len(batch_results),
                "successful_images": len(successful_results),
                "failed_images": len(batch_results) - len(successful_results),
                "total_objects": total_objects,
                "average_percentage": round(sum(percentages) / len(percentages), 2) if percentages else 0.0,
                "max_percentage": round(max(percentages), 2) if percentages else 0.0,
                "min_percentage": round(min(percentages), 2) if percentages else 0.0,
                "total_segmented_area": total_segmented_area,
                "total_image_area": total_image_area,
                "overall_percentage": round((total_segmented_area / total_image_area) * 100, 2) if total_image_area > 0 else 0.0
            }

        except Exception as e:
            logger.error(f"计算批量统计信息时出错: {e}")
            return {
                "total_images": len(batch_results),
                "successful_images": 0,
                "failed_images": len(batch_results),
                "error": str(e)
            }


# 便捷函数
def segmentImage(model_path: str, image_input: Union[str, np.ndarray],
                save_path: str, save_annotated: bool = True,
                conf: float = 0.25, iou: float = 0.7, imgsz: int = 640) -> Dict[str, Any]:
    """
    便捷函数：处理单张图像分割

    Args:
        model_path (str): YOLO模型文件路径
        image_input (Union[str, np.ndarray]): 输入图像（路径或numpy数组）
        save_path (str): 保存路径
        save_annotated (bool): 是否保存标注图像（True=标注图，False=原图）
        conf (float): 置信度阈值
        iou (float): IoU阈值
        imgsz (int): 推理图像大小

    Returns:
        Dict[str, Any]: 处理结果
    """
    service = ImageSegmentationService(model_path)
    return service.processImage(image_input, save_path, save_annotated, conf, iou, imgsz)


def segmentBatchImages(model_path: str, image_inputs: List[Union[str, np.ndarray]],
                      save_dir: str, save_annotated: bool = True,
                      conf: float = 0.25, iou: float = 0.7, imgsz: int = 640,
                      max_workers: int = 4) -> List[Dict[str, Any]]:
    """
    便捷函数：批量处理图像分割

    Args:
        model_path (str): YOLO模型文件路径
        image_inputs (List[Union[str, np.ndarray]]): 输入图像列表
        save_dir (str): 保存目录
        save_annotated (bool): 是否保存标注图像
        conf (float): 置信度阈值
        iou (float): IoU阈值
        imgsz (int): 推理图像大小
        max_workers (int): 最大线程数

    Returns:
        List[Dict[str, Any]]: 处理结果列表
    """
    service = ImageSegmentationService(model_path, max_workers)
    return service.processBatchImages(image_inputs, save_dir, save_annotated, conf, iou, imgsz)


def main():
    """
    示例用法
    """
    # 模型路径
    model_path = "runs/segment/water_segmentation1/weights/best.pt"

    try:
        # 创建服务实例
        service = ImageSegmentationService(model_path, max_workers=4)

        # 示例1：处理单张图像（路径）
        print("=" * 60)
        print("示例1：处理单张图像（路径）")
        print("=" * 60)

        image_path = "path/to/your/image.jpg"  # 替换为实际路径
        save_path = "output/result_image.jpg"

        # 注意：这里只是示例，实际使用时需要提供真实的图像路径
        # result = service.processImage(image_path, save_path, save_annotated=True)
        # print(f"处理结果: {result}")

        # 示例2：处理numpy数组
        print("\n" + "=" * 60)
        print("示例2：处理numpy数组")
        print("=" * 60)

        # 创建一个示例numpy数组（实际使用时替换为真实图像数据）
        # dummy_image = np.random.randint(0, 255, (480, 640, 3), dtype=np.uint8)
        # result = service.processImage(dummy_image, "output/numpy_result.jpg", save_annotated=False)
        # print(f"处理结果: {result}")

        # 示例3：批量处理
        print("\n" + "=" * 60)
        print("示例3：批量处理图像")
        print("=" * 60)

        # 图像路径列表（实际使用时替换为真实路径）
        image_paths = [
            "path/to/image1.jpg",
            "path/to/image2.jpg",
            "path/to/image3.jpg"
        ]

        # 注意：这里只是示例
        # results = service.processBatchImages(image_paths, "output/batch/", save_annotated=True)
        #
        # # 获取批量统计信息
        # stats = service.getBatchStatistics(results)
        # print(f"批量处理统计: {stats}")

        print("示例代码运行完成！")
        print("请根据实际需求修改图像路径和参数。")

    except Exception as e:
        logger.error(f"示例运行失败: {e}")


if __name__ == "__main__":
    main()
