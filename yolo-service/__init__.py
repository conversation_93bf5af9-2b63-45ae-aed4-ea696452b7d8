"""
YOLO推理服务模块

这个模块提供了基于YOLO的多模型推理服务，支持：
- 耙斗检测 (dipper detection)
- 滤池检测 (filter detection) 
- 耙斗井检测 (shaft detection)
- 好氧池检测 (aerobic detection)
- 压榨检测 (piezha detection)
- 图像分割 (segmentation)

特性：
- 统一的API接口
- 线程安全的模型管理
- 配置化的服务启用
- 并发请求处理
- 结果缓存和文件管理
"""

__version__ = "1.0.0"
__author__ = "YOLO Service Team"
__description__ = "基于YOLO的多模型推理Web API服务"

# 导出主要类和函数
from .core.service_manager import ServiceManager
from .core.model_manager import ModelManager
from .core.base_detector import BaseDetector

__all__ = [
    "ServiceManager",
    "ModelManager", 
    "BaseDetector"
]
