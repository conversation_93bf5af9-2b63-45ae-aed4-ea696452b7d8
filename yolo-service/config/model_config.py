"""
模型配置管理

定义各种YOLO模型的配置信息和推理参数
"""

from typing import Dict, Any, List
from dataclasses import dataclass
from enum import Enum


class ModelType(Enum):
    """模型类型枚举"""
    DETECTION = "detection"
    OBB = "obb"  # Oriented Bounding Box
    SEGMENTATION = "segmentation"


class DeviceType(Enum):
    """设备类型枚举"""
    CPU = "cpu"
    GPU = "cuda"
    MPS = "mps"  # Apple Silicon


@dataclass
class InferenceParams:
    """推理参数配置"""
    conf: float = 0.5          # 置信度阈值
    iou: float = 0.4           # IoU阈值
    max_det: int = 10          # 最大检测数量
    imgsz: int = 640           # 输入图像尺寸
    device: str = "cpu"        # 推理设备
    
    def to_dict(self) -> Dict[str, Any]:
        """转换为字典"""
        return {
            'conf': self.conf,
            'iou': self.iou,
            'max_det': self.max_det,
            'imgsz': self.imgsz,
            'device': self.device
        }


@dataclass
class ModelInfo:
    """模型信息配置"""
    name: str                           # 模型名称
    path: str                          # 模型文件路径
    model_type: ModelType              # 模型类型
    inference_params: InferenceParams  # 推理参数
    description: str = ""              # 模型描述
    enabled: bool = True               # 是否启用
    preload: bool = False              # 是否预加载


class ModelConfig:
    """模型配置管理类"""
    
    def __init__(self):
        """初始化模型配置"""
        self._models = self._init_models()
    
    def _init_models(self) -> Dict[str, ModelInfo]:
        """初始化模型配置"""
        return {
            'dipper': ModelInfo(
                name='dipper',
                path='llms/models/yolo/best-dipper.pt',
                model_type=ModelType.DETECTION,
                inference_params=InferenceParams(
                    conf=0.5,
                    iou=0.4,
                    max_det=3,
                    imgsz=640,
                    device='cpu'
                ),
                description='耙斗倾斜检测模型',
                enabled=True,
                preload=True
            ),
            'filter': ModelInfo(
                name='filter',
                path='llms/models/yolo/best-filter.pt',
                model_type=ModelType.DETECTION,
                inference_params=InferenceParams(
                    conf=0.6,
                    iou=0.6,
                    max_det=10,
                    imgsz=640,
                    device='cpu'
                ),
                description='滤池故障检测模型',
                enabled=True,
                preload=True
            ),
            'shaft': ModelInfo(
                name='shaft',
                path='llms/models/yolo/best-dipper-shaft.pt',
                model_type=ModelType.OBB,
                inference_params=InferenceParams(
                    conf=0.25,
                    iou=0.2,
                    max_det=5,
                    imgsz=640,
                    device='cpu'
                ),
                description='耙斗井OBB检测模型',
                enabled=True,
                preload=True
            ),
            'aerobic': ModelInfo(
                name='aerobic',
                path='llms/models/yolo/best-aerobic.pt',
                model_type=ModelType.DETECTION,
                inference_params=InferenceParams(
                    conf=0.5,
                    iou=0.4,
                    max_det=10,
                    imgsz=640,
                    device='cpu'
                ),
                description='好氧池检测模型',
                enabled=False,
                preload=False
            ),
            'piezha': ModelInfo(
                name='piezha',
                path='llms/models/yolo/best-piezha.pt',
                model_type=ModelType.DETECTION,
                inference_params=InferenceParams(
                    conf=0.5,
                    iou=0.4,
                    max_det=10,
                    imgsz=640,
                    device='cpu'
                ),
                description='压榨检测模型',
                enabled=False,
                preload=False
            ),
            'segmentation': ModelInfo(
                name='segmentation',
                path='llms/models/yolo/best.pt',
                model_type=ModelType.SEGMENTATION,
                inference_params=InferenceParams(
                    conf=0.25,
                    iou=0.7,
                    max_det=100,
                    imgsz=640,
                    device='cpu'
                ),
                description='图像分割模型',
                enabled=True,  # 默认启用分割服务
                preload=False
            )
        }
    
    def get_model_info(self, model_name: str) -> ModelInfo:
        """获取模型信息"""
        if model_name not in self._models:
            raise ValueError(f"未知的模型名称: {model_name}")
        return self._models[model_name]
    
    def get_enabled_models(self) -> List[str]:
        """获取启用的模型列表"""
        return [name for name, info in self._models.items() if info.enabled]
    
    def get_preload_models(self) -> List[str]:
        """获取需要预加载的模型列表"""
        return [name for name, info in self._models.items() if info.preload and info.enabled]
    
    def get_models_by_type(self, model_type: ModelType) -> List[str]:
        """根据类型获取模型列表"""
        return [name for name, info in self._models.items() 
                if info.model_type == model_type and info.enabled]
    
    def update_model_config(self, model_name: str, **kwargs):
        """更新模型配置"""
        if model_name not in self._models:
            raise ValueError(f"未知的模型名称: {model_name}")
        
        model_info = self._models[model_name]
        for key, value in kwargs.items():
            if hasattr(model_info, key):
                setattr(model_info, key, value)
            elif hasattr(model_info.inference_params, key):
                setattr(model_info.inference_params, key, value)
    
    def get_all_models(self) -> Dict[str, ModelInfo]:
        """获取所有模型配置"""
        return self._models.copy()


# 创建全局模型配置实例
model_config = ModelConfig()
