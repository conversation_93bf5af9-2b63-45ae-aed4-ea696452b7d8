"""
YOLO服务配置管理

提供统一的配置管理接口，集成现有的config_file.py配置系统
"""

import os
import sys
from pathlib import Path
from typing import Dict, List, Any, Optional

# 添加项目根目录到Python路径
project_root = Path(__file__).parent.parent.parent
sys.path.append(str(project_root))

from config_file import config


class YoloServiceConfig:
    """YOLO服务配置管理类"""
    
    def __init__(self):
        """初始化配置"""
        self._config = config
        self._yolo_config = self._load_yolo_config()
    
    def _load_yolo_config(self) -> Dict[str, Any]:
        """加载YOLO服务配置"""
        # 优先从env.yaml中加载YOLO服务配置
        yolo_config = self._config.env.get('yolo_service', {})

        # 如果env.yaml中没有配置，尝试从config_file.py的yolo_service属性获取
        if not yolo_config and hasattr(self._config, 'yolo_service'):
            logger.info("从config_file.yolo_service加载配置")
            yolo_config = self._config.yolo_service

        # 如果仍然没有配置，使用默认值
        if not yolo_config:
            logger.warning("未找到YOLO服务配置，使用默认配置")
            yolo_config = self._get_default_config()
        else:
            # 合并默认配置，确保所有必要的字段都存在
            default_config = self._get_default_config()
            yolo_config = self._merge_configs(default_config, yolo_config)

        return yolo_config

    def _merge_configs(self, default: Dict[str, Any], user: Dict[str, Any]) -> Dict[str, Any]:
        """
        深度合并配置字典

        Args:
            default: 默认配置
            user: 用户配置

        Returns:
            Dict: 合并后的配置
        """
        merged = default.copy()

        for key, value in user.items():
            if key in merged and isinstance(merged[key], dict) and isinstance(value, dict):
                # 递归合并嵌套字典
                merged[key] = self._merge_configs(merged[key], value)
            else:
                # 直接覆盖
                merged[key] = value

        return merged

    def _get_default_config(self) -> Dict[str, Any]:
        """获取默认配置"""
        return {
            'server': {
                'host': '0.0.0.0',
                'port': 8001,
                'title': 'YOLO推理服务API',
                'description': '基于YOLO的多模型推理Web API服务',
                'version': '1.0.0'
            },
            'models': {
                'paths': {
                    'dipper': 'llms/models/yolo/best-dipper.pt',
                    'filter': 'llms/models/yolo/best-filter.pt',
                    'shaft': 'llms/models/yolo/best-dipper-shaft.pt',
                    'aerobic': 'llms/models/yolo/best-aerobic.pt',
                    'piezha': 'llms/models/yolo/best-piezha.pt',
                    'segmentation': 'llms/models/yolo/best.pt'
                },
                'preload': {
                    'enabled': True,
                    'models': ['dipper', 'filter', 'shaft']
                }
            },
            'enabled_services': {
                'dipper_detection': True,
                'filter_detection': True,
                'shaft_detection': True,
                'aerobic_detection': False,
                'piezha_detection': False,
                'segmentation_detection': True
            },
            'concurrency': {
                'max_workers': 4,
                'max_concurrent_requests': 10,
                'request_timeout': 30
            },
            'file_handling': {
                'upload_dir': 'yolo-service/uploads',
                'output_dir': 'yolo-service/outputs',
                'max_file_size': 10485760,
                'allowed_extensions': ['.jpg', '.jpeg', '.png', '.bmp', '.tiff', '.webp'],
                'cleanup': {
                    'enabled': True,
                    'retention_days': 7
                }
            }
        }
    
    @property
    def host(self) -> str:
        """获取服务主机地址"""
        return self._yolo_config.get('server', {}).get('host', '0.0.0.0')
    
    @property
    def port(self) -> int:
        """获取服务端口"""
        return self._yolo_config.get('server', {}).get('port', 8001)
    
    @property
    def title(self) -> str:
        """获取API标题"""
        return self._yolo_config.get('server', {}).get('title', 'YOLO推理服务API')
    
    @property
    def description(self) -> str:
        """获取API描述"""
        return self._yolo_config.get('server', {}).get('description', '基于YOLO的多模型推理Web API服务')
    
    @property
    def version(self) -> str:
        """获取API版本"""
        return self._yolo_config.get('server', {}).get('version', '1.0.0')
    
    @property
    def model_paths(self) -> Dict[str, str]:
        """获取模型路径配置"""
        return self._yolo_config.get('models', {}).get('paths', {})
    
    @property
    def preload_enabled(self) -> bool:
        """是否启用模型预加载"""
        return self._yolo_config.get('models', {}).get('preload', {}).get('enabled', True)
    
    @property
    def preload_models(self) -> List[str]:
        """获取预加载模型列表"""
        return self._yolo_config.get('models', {}).get('preload', {}).get('models', [])
    
    @property
    def enabled_services(self) -> Dict[str, bool]:
        """获取启用的服务配置"""
        return self._yolo_config.get('enabled_services', {})
    
    @property
    def max_workers(self) -> int:
        """获取最大工作线程数"""
        return self._yolo_config.get('concurrency', {}).get('max_workers', 4)
    
    @property
    def max_concurrent_requests(self) -> int:
        """获取最大并发请求数"""
        return self._yolo_config.get('concurrency', {}).get('max_concurrent_requests', 10)
    
    @property
    def request_timeout(self) -> int:
        """获取请求超时时间"""
        return self._yolo_config.get('concurrency', {}).get('request_timeout', 30)
    
    @property
    def upload_dir(self) -> str:
        """获取上传目录"""
        return self._yolo_config.get('file_handling', {}).get('upload_dir', 'yolo-service/uploads')
    
    @property
    def output_dir(self) -> str:
        """获取输出目录"""
        return self._yolo_config.get('file_handling', {}).get('output_dir', 'yolo-service/outputs')
    
    @property
    def max_file_size(self) -> int:
        """获取最大文件大小"""
        return self._yolo_config.get('file_handling', {}).get('max_file_size', 10485760)
    
    @property
    def allowed_extensions(self) -> List[str]:
        """获取允许的文件扩展名"""
        return self._yolo_config.get('file_handling', {}).get('allowed_extensions', ['.jpg', '.jpeg', '.png'])
    
    @property
    def cleanup_enabled(self) -> bool:
        """是否启用文件清理"""
        return self._yolo_config.get('file_handling', {}).get('cleanup', {}).get('enabled', True)
    
    @property
    def retention_days(self) -> int:
        """获取文件保留天数"""
        return self._yolo_config.get('file_handling', {}).get('cleanup', {}).get('retention_days', 7)
    
    def is_service_enabled(self, service_name: str) -> bool:
        """检查指定服务是否启用"""
        return self.enabled_services.get(service_name, False)
    
    def get_model_path(self, model_name: str) -> Optional[str]:
        """获取指定模型的路径"""
        return self.model_paths.get(model_name)
    
    def get_inference_params(self, model_name: str) -> Dict[str, Any]:
        """获取指定模型的推理参数"""
        return self._yolo_config.get('models', {}).get('inference_params', {}).get(model_name, {})


# 创建全局配置实例
yolo_service_config = YoloServiceConfig()
