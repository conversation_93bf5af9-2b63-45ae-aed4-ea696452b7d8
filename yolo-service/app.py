"""
YOLO推理服务统一入口

实现统一的服务入口，支持配置化启动不同的YOLO服务
"""

import os
import sys
import argparse
import asyncio
from pathlib import Path
from contextlib import asynccontextmanager
import uvicorn
from fastapi import FastAPI

# 添加项目根目录到Python路径
project_root = Path(__file__).parent.parent
sys.path.append(str(project_root))

# 导入YOLO服务组件
from config.service_config import yolo_service_config
from config.model_config import model_config
from core.model_manager import model_manager
from core.service_manager import service_manager
from detectors import DipperDetector, FilterDetector, ShaftDetector, SegmentationDetector
from api.routes import router
from api.middleware import setup_middleware
from api.rate_limiter import concurrency_manager
from utils.logger import get_yolo_logger, YoloServiceLogger

# 初始化日志
logger = get_yolo_logger("main")


@asynccontextmanager
async def lifespan(app: FastAPI):
    """应用生命周期管理"""
    # 启动时初始化
    logger.info("YOLO推理服务启动中...")

    try:
        # 初始化服务
        await initialize_services()
        logger.info("YOLO推理服务启动完成")

        yield

    finally:
        # 关闭时清理
        logger.info("YOLO推理服务关闭中...")
        await cleanup_services()
        logger.info("YOLO推理服务已关闭")


async def initialize_services():
    """初始化所有服务"""
    try:
        # 1. 初始化并发限制器
        await initialize_concurrency_limiter()

        # 2. 注册模型
        await register_models()

        # 3. 预加载模型
        if yolo_service_config.preload_enabled:
            await preload_models()

        # 4. 注册检测器服务
        await register_detector_services()

        # 5. 启动启用的服务
        await start_enabled_services()

        logger.info("所有服务初始化完成")

    except Exception as e:
        logger.error(f"服务初始化失败: {e}")
        raise


async def initialize_concurrency_limiter():
    """初始化并发限制器"""
    logger.info("初始化并发限制器...")

    # 设置全局并发限制
    max_concurrent = yolo_service_config.max_concurrent_requests
    concurrency_manager.set_global_limit(max_concurrent)

    # 为每个模型设置特定的并发限制（可选）
    enabled_services = yolo_service_config.enabled_services
    for service_name, enabled in enabled_services.items():
        if enabled:
            model_name = service_name.replace('_detection', '')
            # 每个模型的并发限制设为全局限制的一半
            model_limit = max(1, max_concurrent // 2)
            concurrency_manager.set_model_limit(model_name, model_limit)
            logger.info(f"模型 {model_name} 并发限制设置为: {model_limit}")

    logger.info(f"并发限制器初始化完成，全局限制: {max_concurrent}")


async def register_models():
    """注册模型"""
    logger.info("注册模型...")

    # 获取所有模型配置
    all_models = model_config.get_all_models()

    for model_name, model_info in all_models.items():
        try:
            # 注册模型到模型管理器
            model_manager.register_model(
                model_name=model_name,
                model_path=model_info.path,
                task_type=model_info.model_type.value
            )

            logger.info(f"模型已注册: {model_name} -> {model_info.path}")

        except Exception as e:
            logger.error(f"注册模型失败: {model_name}, 错误: {e}")


async def preload_models():
    """预加载模型"""
    logger.info("预加载模型...")

    preload_list = model_config.get_preload_models()

    if not preload_list:
        logger.info("没有需要预加载的模型")
        return

    # 批量预加载模型
    results = model_manager.preload_models(preload_list)

    success_count = sum(1 for success in results.values() if success)
    total_count = len(results)

    logger.info(f"模型预加载完成: {success_count}/{total_count} 成功")

    # 记录失败的模型
    for model_name, success in results.items():
        if not success:
            logger.warning(f"模型预加载失败: {model_name}")


async def register_detector_services():
    """注册检测器服务"""
    logger.info("注册检测器服务...")

    # 服务映射
    service_mapping = {
        'dipper_detection': DipperDetector,
        'filter_detection': FilterDetector,
        'shaft_detection': ShaftDetector,
        'segmentation_detection': SegmentationDetector  # 统一命名为 xxx_detection
    }

    for service_name, detector_class in service_mapping.items():
        try:
            # 获取对应的模型名称
            model_name = service_name.replace('_detection', '')

            # 获取模型信息
            model_info = model_config.get_model_info(model_name)

            # 注册服务
            service_manager.register_service(
                service_name=service_name,
                service_class=detector_class,
                config={'model_info': model_info}
            )

            logger.info(f"检测器服务已注册: {service_name}")

        except Exception as e:
            logger.error(f"注册检测器服务失败: {service_name}, 错误: {e}")


async def start_enabled_services():
    """启动启用的服务"""
    logger.info("启动启用的服务...")

    enabled_services = yolo_service_config.enabled_services

    # 启动启用的服务
    results = service_manager.start_enabled_services({'enabled_services': enabled_services})

    success_count = sum(1 for success in results.values() if success)
    total_count = len([s for s in enabled_services.values() if s])

    logger.info(f"服务启动完成: {success_count}/{total_count} 成功")

    # 记录启动结果
    for service_name, success in results.items():
        if enabled_services.get(service_name, False):
            if success:
                logger.info(f"服务启动成功: {service_name}")
            else:
                logger.error(f"服务启动失败: {service_name}")


async def cleanup_services():
    """清理服务"""
    try:
        # 停止所有服务
        service_manager.stop_all_services()

        # 卸载所有模型
        model_manager.unload_all_models()

        # 清理日志记录器
        YoloServiceLogger.cleanup_loggers()

        logger.info("服务清理完成")

    except Exception as e:
        logger.error(f"服务清理失败: {e}")


def create_app() -> FastAPI:
    """创建FastAPI应用"""
    # 创建FastAPI应用
    app = FastAPI(
        title=yolo_service_config.title,
        description=yolo_service_config.description,
        version=yolo_service_config.version,
        docs_url="/docs",
        redoc_url="/redoc",
        lifespan=lifespan
    )

    # 设置中间件
    setup_middleware(app, {
        'cors': {
            'enabled': True,
            'origins': ["*"],
            'credentials': True,
            'methods': ["*"],
            'headers': ["*"]
        },
        'rate_limit': {
            'enabled': False,  # 可根据需要启用
            'max_requests': 100,
            'window_seconds': 60
        }
    })

    # 添加路由
    app.include_router(router, prefix="/api/v1")

    # 根路径重定向到API文档
    @app.get("/")
    async def root():
        return {
            "message": "YOLO推理服务API",
            "version": yolo_service_config.version,
            "docs": "/docs",
            "api": "/api/v1"
        }

    return app


def parse_arguments():
    """解析命令行参数"""
    parser = argparse.ArgumentParser(description="YOLO推理服务")

    parser.add_argument("--host", default=yolo_service_config.host,
                       help=f"服务器主机地址 (默认: {yolo_service_config.host})")
    parser.add_argument("--port", type=int, default=yolo_service_config.port,
                       help=f"服务器端口 (默认: {yolo_service_config.port})")
    parser.add_argument("--workers", type=int, default=1,
                       help="工作进程数 (默认: 1)")
    parser.add_argument("--reload", action="store_true",
                       help="开发模式，自动重载")
    parser.add_argument("--log-level", default="info",
                       choices=["debug", "info", "warning", "error", "critical"],
                       help="日志级别 (默认: info)")
    parser.add_argument("--preload", action="store_true",
                       help="强制预加载所有启用的模型")
    parser.add_argument("--no-preload", action="store_true",
                       help="禁用模型预加载")

    return parser.parse_args()


def main():
    """主函数"""
    args = parse_arguments()

    # 设置日志级别
    YoloServiceLogger.set_log_level(args.log_level.upper())

    # 处理预加载选项
    if args.preload:
        # 强制启用所有模型的预加载
        for model_name in model_config.get_enabled_models():
            model_config.update_model_config(model_name, preload=True)
    elif args.no_preload:
        # 禁用所有模型的预加载
        for model_name in model_config.get_enabled_models():
            model_config.update_model_config(model_name, preload=False)

    # 创建应用
    app = create_app()

    # 打印启动信息
    print("=" * 60)
    print("🚀 YOLO推理服务")
    print("=" * 60)
    print(f"📍 服务地址: http://{args.host}:{args.port}")
    print(f"📚 API文档: http://{args.host}:{args.port}/docs")
    print(f"🔄 ReDoc文档: http://{args.host}:{args.port}/redoc")
    print(f"🔧 API端点: http://{args.host}:{args.port}/api/v1")
    print(f"📊 启用的服务: {list(yolo_service_config.enabled_services.keys())}")
    print(f"🧵 最大工作线程数: {yolo_service_config.max_workers}")
    print(f"📁 上传目录: {yolo_service_config.upload_dir}")
    print(f"📁 输出目录: {yolo_service_config.output_dir}")
    print("=" * 60)

    # 启动服务
    uvicorn.run(
        app,
        host=args.host,
        port=args.port,
        workers=args.workers,
        reload=args.reload,
        log_level=args.log_level,
        access_log=True
    )


if __name__ == "__main__":
    main()