"""
基本功能测试

测试YOLO服务的基本功能是否正常工作
"""

import os
import sys
import unittest
import tempfile
import numpy as np
import cv2
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent.parent.parent
sys.path.append(str(project_root))

# 导入测试目标
sys.path.append(str(Path(__file__).parent.parent))
from config.service_config import yolo_service_config
from config.model_config import model_config
from core.model_manager import model_manager
from core.service_manager import service_manager
from utils.image_processor import ImageProcessor
from utils.result_formatter import ResultFormatter


class TestBasicFunctionality(unittest.TestCase):
    """基本功能测试类"""
    
    @classmethod
    def setUpClass(cls):
        """测试类初始化"""
        print("开始基本功能测试...")
        
        # 创建测试图像
        cls.test_image = cls.create_test_image()
        cls.test_image_path = cls.save_test_image(cls.test_image)
    
    @classmethod
    def tearDownClass(cls):
        """测试类清理"""
        # 清理测试文件
        if hasattr(cls, 'test_image_path') and os.path.exists(cls.test_image_path):
            os.remove(cls.test_image_path)
        
        print("基本功能测试完成")
    
    @staticmethod
    def create_test_image(width=640, height=480):
        """创建测试图像"""
        # 创建一个简单的测试图像
        image = np.random.randint(0, 255, (height, width, 3), dtype=np.uint8)
        
        # 添加一些简单的几何形状
        cv2.rectangle(image, (100, 100), (200, 200), (255, 0, 0), 2)
        cv2.circle(image, (400, 300), 50, (0, 255, 0), 2)
        
        return image
    
    @staticmethod
    def save_test_image(image):
        """保存测试图像"""
        with tempfile.NamedTemporaryFile(suffix='.jpg', delete=False) as f:
            cv2.imwrite(f.name, image)
            return f.name
    
    def test_config_loading(self):
        """测试配置加载"""
        print("测试配置加载...")
        
        # 测试服务配置
        self.assertIsNotNone(yolo_service_config.host)
        self.assertIsNotNone(yolo_service_config.port)
        self.assertIsInstance(yolo_service_config.enabled_services, dict)
        
        # 测试模型配置
        models = model_config.get_all_models()
        self.assertIsInstance(models, dict)
        self.assertGreater(len(models), 0)
        
        print("✓ 配置加载测试通过")
    
    def test_image_processor(self):
        """测试图像处理器"""
        print("测试图像处理器...")
        
        # 测试图像加载
        loaded_image, path = ImageProcessor.load_image(self.test_image_path)
        self.assertIsNotNone(loaded_image)
        self.assertEqual(path, self.test_image_path)
        
        # 测试图像信息获取
        info = ImageProcessor.get_image_info(loaded_image)
        self.assertIn('width', info)
        self.assertIn('height', info)
        self.assertIn('channels', info)
        
        # 测试图像尺寸验证
        is_valid = ImageProcessor.validate_image_size(loaded_image)
        self.assertTrue(is_valid)
        
        # 测试图像调整大小
        resized = ImageProcessor.resize_image(loaded_image, (320, 240))
        self.assertEqual(resized.shape[:2], (240, 320))
        
        print("✓ 图像处理器测试通过")
    
    def test_result_formatter(self):
        """测试结果格式化器"""
        print("测试结果格式化器...")
        
        # 测试检测结果格式化
        devices = [
            {"device_id": 1, "status": "normal", "confidence": 0.85, "bbox": [100, 100, 200, 200]},
            {"device_id": 2, "status": "abnormal", "confidence": 0.92, "bbox": [300, 150, 400, 250]}
        ]
        
        result = ResultFormatter.format_detection_result(
            success=True,
            devices=devices,
            model_name="test_model",
            processing_time=1.5
        )
        
        self.assertTrue(result['success'])
        self.assertEqual(len(result['devices']), 2)
        self.assertEqual(result['model_name'], "test_model")
        self.assertAlmostEqual(result['processing_time'], 1.5)
        
        # 测试设备信息格式化
        device_info = ResultFormatter.format_device_info(
            device_id=1,
            status="normal",
            confidence=0.85,
            bbox=[100, 100, 200, 200],
            position="位置1"
        )
        
        self.assertEqual(device_info['device_id'], 1)
        self.assertEqual(device_info['status'], "normal")
        self.assertEqual(device_info['position'], "位置1")
        
        print("✓ 结果格式化器测试通过")
    
    def test_model_manager(self):
        """测试模型管理器"""
        print("测试模型管理器...")
        
        # 测试模型注册
        test_model_path = "test_model.pt"
        model_manager.register_model(
            model_name="test_model",
            model_path=test_model_path,
            task_type="detect"
        )
        
        # 检查模型是否已注册
        registered_models = model_manager.get_registered_models()
        self.assertIn("test_model", registered_models)
        
        # 测试统计信息
        stats = model_manager.get_all_stats()
        self.assertIn("total_registered", stats)
        self.assertIn("total_loaded", stats)
        
        print("✓ 模型管理器测试通过")
    
    def test_service_manager(self):
        """测试服务管理器"""
        print("测试服务管理器...")
        
        # 创建一个简单的测试服务类
        class TestService:
            def __init__(self, **kwargs):
                self.config = kwargs
                self.running = True
            
            def stop(self):
                self.running = False
            
            def health_check(self):
                return {
                    "service": "test_service",
                    "status": "running",
                    "healthy": self.running,
                    "message": "测试服务运行正常"
                }
        
        # 注册测试服务
        service_manager.register_service(
            service_name="test_service",
            service_class=TestService,
            config={"test_param": "test_value"}
        )
        
        # 启动服务
        success = service_manager.start_service("test_service")
        self.assertTrue(success)
        
        # 检查服务状态
        self.assertTrue(service_manager.is_service_running("test_service"))
        
        # 健康检查
        health = service_manager.health_check("test_service")
        self.assertTrue(health['healthy'])
        
        # 停止服务
        success = service_manager.stop_service("test_service")
        self.assertTrue(success)
        
        print("✓ 服务管理器测试通过")
    
    def test_error_handling(self):
        """测试错误处理"""
        print("测试错误处理...")
        
        # 测试无效图像路径
        with self.assertRaises(FileNotFoundError):
            ImageProcessor.load_image("nonexistent_image.jpg")
        
        # 测试无效模型名称
        model_info = model_manager.get_model("nonexistent_model", auto_load=False)
        self.assertIsNone(model_info)
        
        # 测试错误结果格式化
        error_result = ResultFormatter.format_error_result(
            error="测试错误",
            model_name="test_model",
            processing_time=0.5
        )
        
        self.assertFalse(error_result['success'])
        self.assertEqual(error_result['error'], "测试错误")
        
        print("✓ 错误处理测试通过")


def run_basic_tests():
    """运行基本功能测试"""
    print("=" * 60)
    print("🧪 YOLO服务基本功能测试")
    print("=" * 60)
    
    # 创建测试套件
    suite = unittest.TestLoader().loadTestsFromTestCase(TestBasicFunctionality)
    
    # 运行测试
    runner = unittest.TextTestRunner(verbosity=2)
    result = runner.run(suite)
    
    # 输出结果
    print("\n" + "=" * 60)
    if result.wasSuccessful():
        print("✅ 所有基本功能测试通过！")
    else:
        print("❌ 部分测试失败")
        print(f"失败数量: {len(result.failures)}")
        print(f"错误数量: {len(result.errors)}")
    print("=" * 60)
    
    return result.wasSuccessful()


if __name__ == "__main__":
    run_basic_tests()
