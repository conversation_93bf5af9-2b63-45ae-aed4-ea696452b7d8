"""
服务集成测试

测试YOLO服务的完整集成功能
"""

import os
import sys
import time
import requests
import base64
import tempfile
import numpy as np
import cv2
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent.parent.parent
sys.path.append(str(project_root))


class YoloServiceTester:
    """YOLO服务测试器"""
    
    def __init__(self, base_url="http://localhost:8001"):
        self.base_url = base_url
        self.api_url = f"{base_url}/api/v1"
        
    def create_test_image(self, width=640, height=480):
        """创建测试图像"""
        # 创建一个简单的测试图像
        image = np.random.randint(0, 255, (height, width, 3), dtype=np.uint8)
        
        # 添加一些几何形状
        cv2.rectangle(image, (100, 100), (200, 200), (255, 0, 0), 2)
        cv2.circle(image, (400, 300), 50, (0, 255, 0), 2)
        cv2.putText(image, "TEST", (300, 100), cv2.FONT_HERSHEY_SIMPLEX, 2, (0, 0, 255), 3)
        
        return image
    
    def save_test_image(self, image):
        """保存测试图像"""
        with tempfile.NamedTemporaryFile(suffix='.jpg', delete=False) as f:
            cv2.imwrite(f.name, image)
            return f.name
    
    def image_to_base64(self, image):
        """将图像转换为Base64"""
        _, buffer = cv2.imencode('.jpg', image)
        image_bytes = buffer.tobytes()
        return base64.b64encode(image_bytes).decode('utf-8')
    
    def test_health_check(self):
        """测试健康检查"""
        print("🔍 测试健康检查...")
        
        try:
            response = requests.get(f"{self.api_url}/health", timeout=10)
            
            if response.status_code == 200:
                data = response.json()
                print(f"✅ 健康检查通过: {data.get('status', 'unknown')}")
                print(f"   已加载模型: {data.get('loaded_models', [])}")
                print(f"   启用服务: {list(data.get('enabled_services', {}).keys())}")
                return True
            else:
                print(f"❌ 健康检查失败: HTTP {response.status_code}")
                return False
                
        except requests.exceptions.RequestException as e:
            print(f"❌ 健康检查连接失败: {e}")
            return False
    
    def test_upload_detection(self, model_type="dipper"):
        """测试文件上传检测"""
        print(f"🔍 测试文件上传检测 ({model_type})...")
        
        try:
            # 创建测试图像
            test_image = self.create_test_image()
            image_path = self.save_test_image(test_image)
            
            try:
                # 上传文件
                with open(image_path, 'rb') as f:
                    files = {'file': ('test_image.jpg', f, 'image/jpeg')}
                    data = {
                        'model_type': model_type,
                        'save_result': 'true',
                        'conf': '0.5'
                    }
                    
                    response = requests.post(
                        f"{self.api_url}/detect/upload",
                        files=files,
                        data=data,
                        timeout=30
                    )
                
                if response.status_code == 200:
                    result = response.json()
                    print(f"✅ 上传检测成功")
                    print(f"   模型: {result.get('model_name', 'unknown')}")
                    print(f"   检测数量: {len(result.get('devices', []))}")
                    print(f"   处理时间: {result.get('processing_time', 0):.2f}s")
                    
                    if result.get('download_url'):
                        print(f"   下载链接: {self.base_url}{result['download_url']}")
                    
                    return True
                else:
                    print(f"❌ 上传检测失败: HTTP {response.status_code}")
                    print(f"   错误信息: {response.text}")
                    return False
                    
            finally:
                # 清理测试文件
                if os.path.exists(image_path):
                    os.remove(image_path)
                    
        except Exception as e:
            print(f"❌ 上传检测异常: {e}")
            return False
    
    def test_base64_detection(self, model_type="filter"):
        """测试Base64检测"""
        print(f"🔍 测试Base64检测 ({model_type})...")
        
        try:
            # 创建测试图像
            test_image = self.create_test_image()
            base64_data = self.image_to_base64(test_image)
            
            # 发送Base64请求
            payload = {
                "model_type": model_type,
                "image_data": base64_data,
                "save_result": False,
                "conf": 0.6
            }
            
            response = requests.post(
                f"{self.api_url}/detect/base64",
                json=payload,
                timeout=30
            )
            
            if response.status_code == 200:
                result = response.json()
                print(f"✅ Base64检测成功")
                print(f"   模型: {result.get('model_name', 'unknown')}")
                print(f"   检测数量: {len(result.get('devices', []))}")
                print(f"   处理时间: {result.get('processing_time', 0):.2f}s")
                return True
            else:
                print(f"❌ Base64检测失败: HTTP {response.status_code}")
                print(f"   错误信息: {response.text}")
                return False
                
        except Exception as e:
            print(f"❌ Base64检测异常: {e}")
            return False
    
    def test_models_endpoint(self):
        """测试模型信息端点"""
        print("🔍 测试模型信息端点...")
        
        try:
            response = requests.get(f"{self.api_url}/models", timeout=10)
            
            if response.status_code == 200:
                models = response.json()
                print(f"✅ 模型信息获取成功")
                print(f"   模型数量: {len(models)}")
                
                for model in models:
                    status = "✅" if model.get('loaded') else "⏸️"
                    enabled = "启用" if model.get('enabled') else "禁用"
                    print(f"   {status} {model.get('name', 'unknown')} ({enabled})")
                
                return True
            else:
                print(f"❌ 模型信息获取失败: HTTP {response.status_code}")
                return False
                
        except Exception as e:
            print(f"❌ 模型信息获取异常: {e}")
            return False
    
    def test_services_endpoint(self):
        """测试服务状态端点"""
        print("🔍 测试服务状态端点...")
        
        try:
            response = requests.get(f"{self.api_url}/services", timeout=10)
            
            if response.status_code == 200:
                services = response.json()
                print(f"✅ 服务状态获取成功")
                print(f"   服务数量: {len(services)}")
                
                for service in services:
                    status = "✅" if service.get('healthy') else "❌"
                    print(f"   {status} {service.get('service_name', 'unknown')} ({service.get('status', 'unknown')})")
                
                return True
            else:
                print(f"❌ 服务状态获取失败: HTTP {response.status_code}")
                return False
                
        except Exception as e:
            print(f"❌ 服务状态获取异常: {e}")
            return False
    
    def test_concurrent_requests(self, num_requests=5):
        """测试并发请求"""
        print(f"🔍 测试并发请求 ({num_requests} 个)...")
        
        import threading
        import queue
        
        results = queue.Queue()
        
        def make_request():
            try:
                test_image = self.create_test_image()
                base64_data = self.image_to_base64(test_image)
                
                payload = {
                    "model_type": "dipper",
                    "image_data": base64_data,
                    "save_result": False
                }
                
                start_time = time.time()
                response = requests.post(
                    f"{self.api_url}/detect/base64",
                    json=payload,
                    timeout=30
                )
                end_time = time.time()
                
                results.put({
                    'success': response.status_code == 200,
                    'time': end_time - start_time,
                    'status_code': response.status_code
                })
                
            except Exception as e:
                results.put({
                    'success': False,
                    'time': 0,
                    'error': str(e)
                })
        
        # 启动并发请求
        threads = []
        start_time = time.time()
        
        for i in range(num_requests):
            thread = threading.Thread(target=make_request)
            thread.start()
            threads.append(thread)
        
        # 等待所有线程完成
        for thread in threads:
            thread.join()
        
        total_time = time.time() - start_time
        
        # 收集结果
        success_count = 0
        total_request_time = 0
        
        while not results.empty():
            result = results.get()
            if result['success']:
                success_count += 1
                total_request_time += result['time']
        
        print(f"✅ 并发测试完成")
        print(f"   成功率: {success_count}/{num_requests} ({success_count/num_requests*100:.1f}%)")
        print(f"   总耗时: {total_time:.2f}s")
        print(f"   平均请求时间: {total_request_time/max(success_count, 1):.2f}s")
        
        return success_count == num_requests
    
    def run_all_tests(self):
        """运行所有测试"""
        print("=" * 60)
        print("🧪 YOLO服务集成测试")
        print("=" * 60)
        
        tests = [
            ("健康检查", self.test_health_check),
            ("模型信息", self.test_models_endpoint),
            ("服务状态", self.test_services_endpoint),
            ("上传检测", lambda: self.test_upload_detection("dipper")),
            ("Base64检测", lambda: self.test_base64_detection("filter")),
            ("并发请求", lambda: self.test_concurrent_requests(3))
        ]
        
        passed = 0
        total = len(tests)
        
        for test_name, test_func in tests:
            print(f"\n📋 {test_name}")
            print("-" * 40)
            
            try:
                if test_func():
                    passed += 1
                    print(f"✅ {test_name} 通过")
                else:
                    print(f"❌ {test_name} 失败")
            except Exception as e:
                print(f"❌ {test_name} 异常: {e}")
            
            time.sleep(1)  # 短暂等待
        
        print("\n" + "=" * 60)
        print(f"📊 测试结果: {passed}/{total} 通过 ({passed/total*100:.1f}%)")
        
        if passed == total:
            print("🎉 所有测试通过！服务运行正常")
        else:
            print("⚠️  部分测试失败，请检查服务状态")
        
        print("=" * 60)
        
        return passed == total


def main():
    """主函数"""
    import argparse
    
    parser = argparse.ArgumentParser(description="YOLO服务集成测试")
    parser.add_argument("--url", default="http://localhost:8001", help="服务URL")
    parser.add_argument("--wait", type=int, default=5, help="等待服务启动的时间(秒)")
    
    args = parser.parse_args()
    
    print(f"🔗 连接到服务: {args.url}")
    
    if args.wait > 0:
        print(f"⏳ 等待 {args.wait} 秒让服务完全启动...")
        time.sleep(args.wait)
    
    # 创建测试器并运行测试
    tester = YoloServiceTester(args.url)
    success = tester.run_all_tests()
    
    return 0 if success else 1


if __name__ == "__main__":
    sys.exit(main())
